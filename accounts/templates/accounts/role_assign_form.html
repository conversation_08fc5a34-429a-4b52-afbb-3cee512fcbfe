{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    .select2-container--default .select2-selection--multiple {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
        min-height: 38px;
    }
    .role-info {
        background-color: #f8f9fc;
        border-radius: 0.35rem;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{{ title }}</h6>
            <a href="{% url 'accounts:role_list' %}" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة إلى قائمة الأدوار
            </a>
        </div>
        <div class="card-body">
            <!-- معلومات الدور -->
            <div class="role-info">
                <h5><i class="fas fa-user-tag"></i> معلومات الدور</h5>
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">اسم الدور:</dt>
                            <dd class="col-sm-8">{{ role.name }}</dd>
                            
                            <dt class="col-sm-4">نوع الدور:</dt>
                            <dd class="col-sm-8">
                                {% if role.is_system_role %}
                                <span class="badge bg-primary">نظام</span>
                                {% else %}
                                <span class="badge bg-secondary">مخصص</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-6">عدد الصلاحيات:</dt>
                            <dd class="col-sm-6">{{ role.permissions.count }}</dd>
                            
                            <dt class="col-sm-6">عدد المستخدمين الحاليين:</dt>
                            <dd class="col-sm-6">{{ role.user_roles.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            
            <form method="post" action="">
                {% csrf_token %}
                
                <!-- رسائل الخطأ -->
                {% if form.errors %}
                <div class="alert alert-danger">
                    <strong>هناك أخطاء في النموذج:</strong>
                    <ul>
                    {% for field in form %}
                        {% for error in field.errors %}
                        <li>{{ field.label }}: {{ error }}</li>
                        {% endfor %}
                    {% endfor %}
                    </ul>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <label for="{{ form.users.id_for_label }}" class="form-label">اختر المستخدمين <span class="text-danger">*</span></label>
                    {{ form.users }}
                    <small class="form-text text-muted">اختر المستخدمين الذين تريد منحهم هذا الدور</small>
                </div>
                
                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> إسناد الدور للمستخدمين
                    </button>
                    <a href="{% url 'accounts:role_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2({
            placeholder: "اختر المستخدمين...",
            allowClear: true
        });
    });
</script>
{% endblock %}