{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة التعارضات{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'odoo_db_manager/css/style.css' %}">
<style>
    .conflict-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .conflict-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-left: 5px solid #dc3545;
        transition: transform 0.3s ease;
    }

    .conflict-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }

    .conflict-type {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .type-data_mismatch { background: #ffeaa7; color: #d63031; }
    .type-concurrent_edit { background: #fab1a0; color: #e17055; }
    .type-validation_error { background: #fd79a8; color: #e84393; }
    .type-duplicate_record { background: #fdcb6e; color: #e17055; }

    .resolution-status {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .status-pending { background: #ffc107; color: #856404; }
    .status-resolved { background: #28a745; color: white; }
    .status-ignored { background: #6c757d; color: white; }

    .conflict-meta {
        color: #6c757d;
        font-size: 14px;
        margin-top: 10px;
    }

    .conflict-description {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        border-left: 3px solid #dc3545;
    }

    .conflict-actions {
        margin-top: 15px;
    }

    .btn-resolve {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        transition: all 0.3s ease;
    }

    .btn-resolve:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .btn-ignore {
        background: linear-gradient(45deg, #6c757d, #5a6268);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        transition: all 0.3s ease;
    }

    .btn-ignore:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
        color: white;
    }

    .filter-section {
        background: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #6c757d;
        font-size: 14px;
    }

    .pending-conflicts { color: #ffc107; }
    .resolved-conflicts { color: #28a745; }
    .total-conflicts { color: #dc3545; }

    .arabic-text {
        font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Lucida Sans Unicode', sans-serif;
        direction: rtl;
        text-align: right;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid arabic-text">
    <!-- رأس الصفحة -->
    <div class="conflict-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-exclamation-triangle"></i>
                    قائمة التعارضات
                </h1>
                <p class="mb-0 opacity-75">
                    إدارة وحل تعارضات المزامنة مع Google Sheets
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'odoo_db_manager:advanced_sync_dashboard' %}" class="btn btn-light">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>
    </div>

    <!-- إحصائيات التعارضات -->
    <div class="stats-row">
        <div class="stat-card">
            <div class="stat-number pending-conflicts">{{ pending_count }}</div>
            <div class="stat-label">تعارضات معلقة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number resolved-conflicts">{{ resolved_count }}</div>
            <div class="stat-label">تعارضات محلولة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number total-conflicts">{{ total_count }}</div>
            <div class="stat-label">إجمالي التعارضات</div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filter-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">نوع التعارض</label>
                <select name="conflict_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="data_mismatch" {% if request.GET.conflict_type == 'data_mismatch' %}selected{% endif %}>
                        عدم تطابق البيانات
                    </option>
                    <option value="concurrent_edit" {% if request.GET.conflict_type == 'concurrent_edit' %}selected{% endif %}>
                        تعديل متزامن
                    </option>
                    <option value="validation_error" {% if request.GET.conflict_type == 'validation_error' %}selected{% endif %}>
                        خطأ في التحقق
                    </option>
                    <option value="duplicate_record" {% if request.GET.conflict_type == 'duplicate_record' %}selected{% endif %}>
                        سجل مكرر
                    </option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">حالة الحل</label>
                <select name="resolution_status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {% if request.GET.resolution_status == 'pending' %}selected{% endif %}>
                        معلق
                    </option>
                    <option value="resolved" {% if request.GET.resolution_status == 'resolved' %}selected{% endif %}>
                        محلول
                    </option>
                    <option value="ignored" {% if request.GET.resolution_status == 'ignored' %}selected{% endif %}>
                        متجاهل
                    </option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع السجل</label>
                <input type="text" name="record_type" class="form-control" 
                       value="{{ request.GET.record_type }}" placeholder="مثل: customer, order">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    <a href="{% url 'odoo_db_manager:conflict_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- قائمة التعارضات -->
    {% if conflicts %}
        {% for conflict in conflicts %}
        <div class="conflict-card">
            <div class="row">
                <div class="col-md-8">
                    <div class="d-flex align-items-center mb-2">
                        <span class="conflict-type type-{{ conflict.conflict_type }}">
                            {{ conflict.get_conflict_type_display|default:conflict.conflict_type }}
                        </span>
                        <span class="resolution-status status-{{ conflict.resolution_status }} me-3">
                            {{ conflict.get_resolution_status_display|default:conflict.resolution_status }}
                        </span>
                    </div>
                    
                    <h5 class="mb-2">
                        تعارض في {{ conflict.record_type }} - صف {{ conflict.sheet_row }}
                    </h5>
                    
                    {% if conflict.conflict_description %}
                    <div class="conflict-description">
                        {{ conflict.conflict_description }}
                    </div>
                    {% endif %}
                    
                    <div class="conflict-meta">
                        <i class="fas fa-table"></i>
                        <strong>المهمة:</strong> {{ conflict.task.mapping.name }}
                        {% if conflict.record_id %}
                        | <i class="fas fa-id-card"></i>
                        <strong>معرف السجل:</strong> {{ conflict.record_id }}
                        {% endif %}
                        | <i class="fas fa-clock"></i>
                        <strong>التاريخ:</strong> {{ conflict.created_at|date:"Y-m-d H:i" }}
                    </div>
                    
                    {% if conflict.resolution_status == 'resolved' and conflict.resolved_by %}
                    <div class="conflict-meta mt-2">
                        <i class="fas fa-user-check"></i>
                        <strong>تم الحل بواسطة:</strong> {{ conflict.resolved_by.get_full_name|default:conflict.resolved_by.username }}
                        {% if conflict.resolved_at %}
                        في {{ conflict.resolved_at|date:"Y-m-d H:i" }}
                        {% endif %}
                        {% if conflict.resolution_notes %}
                        <br><strong>ملاحظات:</strong> {{ conflict.resolution_notes }}
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-4">
                    {% if conflict.resolution_status == 'pending' %}
                    <div class="conflict-actions">
                        <button class="btn btn-resolve btn-sm" 
                                onclick="resolveConflict({{ conflict.id }}, 'resolved')">
                            <i class="fas fa-check"></i>
                            حل التعارض
                        </button>
                        <button class="btn btn-ignore btn-sm" 
                                onclick="resolveConflict({{ conflict.id }}, 'ignored')">
                            <i class="fas fa-times"></i>
                            تجاهل
                        </button>
                    </div>
                    {% endif %}
                    
                    <div class="mt-3">
                        <a href="{% url 'odoo_db_manager:task_detail' conflict.task.id %}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i>
                            عرض المهمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}

        <!-- Pagination -->
        {% if is_paginated %}
        <nav aria-label="صفحات التعارضات">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.conflict_type %}&conflict_type={{ request.GET.conflict_type }}{% endif %}{% if request.GET.resolution_status %}&resolution_status={{ request.GET.resolution_status }}{% endif %}{% if request.GET.record_type %}&record_type={{ request.GET.record_type }}{% endif %}">
                        السابق
                    </a>
                </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}{% if request.GET.conflict_type %}&conflict_type={{ request.GET.conflict_type }}{% endif %}{% if request.GET.resolution_status %}&resolution_status={{ request.GET.resolution_status }}{% endif %}{% if request.GET.record_type %}&record_type={{ request.GET.record_type }}{% endif %}">
                        {{ num }}
                    </a>
                </li>
                {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.conflict_type %}&conflict_type={{ request.GET.conflict_type }}{% endif %}{% if request.GET.resolution_status %}&resolution_status={{ request.GET.resolution_status }}{% endif %}{% if request.GET.record_type %}&record_type={{ request.GET.record_type }}{% endif %}">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

    {% else %}
        <div class="empty-state">
            <i class="fas fa-check-circle"></i>
            <h3>لا توجد تعارضات</h3>
            <p>جميع عمليات المزامنة تمت بنجاح بدون تعارضات.</p>
        </div>
    {% endif %}
</div>

<!-- Modal لحل التعارض -->
<div class="modal fade" id="resolveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حل التعارض</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="resolveForm">
                {% csrf_token %}
                <div class="modal-body">
                    <input type="hidden" id="conflictId" name="conflict_id">
                    <input type="hidden" id="resolutionStatus" name="resolution_status">
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات الحل</label>
                        <textarea class="form-control" name="resolution_notes" rows="3" 
                                  placeholder="اكتب ملاحظات حول كيفية حل هذا التعارض..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تأكيد الحل</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function resolveConflict(conflictId, status) {
    document.getElementById('conflictId').value = conflictId;
    document.getElementById('resolutionStatus').value = status;
    
    const modal = new bootstrap.Modal(document.getElementById('resolveModal'));
    modal.show();
}

document.getElementById('resolveForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const conflictId = formData.get('conflict_id');
    
    fetch(`/odoo-db-manager/advanced-sync/conflicts/${conflictId}/resolve/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': formData.get('csrfmiddlewaretoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
});
</script>
{% endblock %}
