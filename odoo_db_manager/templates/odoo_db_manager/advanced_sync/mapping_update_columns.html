{% extends 'base.html' %}
{% load static %}

{% block title %}تحديث تعيينات الأعمدة{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'odoo_db_manager/css/style.css' %}">
<style>
    .column-mapping-row {
        display: flex;
        align-items: center;
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background: #f9f9f9;
    }

    .column-name {
        flex: 1;
        font-weight: bold;
        color: #333;
        margin-right: 15px;
    }

    .field-select {
        flex: 2;
        margin-right: 10px;
    }

    .mapping-container {
        max-height: 600px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        background: white;
    }

    .arabic-text {
        font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Lucida Sans Unicode', sans-serif;
        direction: rtl;
        text-align: right;
    }

    .btn-save {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 12px 30px;
        font-size: 16px;
        border-radius: 25px;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        transition: all 0.3s ease;
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        color: white;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- شريط الأدوات -->
    <div class="toolbar">
        <h1>
            <i class="fas fa-columns"></i>
            تحديث تعيينات الأعمدة
        </h1>
        <div>
            <a href="{% url 'odoo_db_manager:mapping_detail' mapping.id %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للتفاصيل
            </a>
        </div>
    </div>

    <!-- معلومات التعيين -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-info-circle"></i>
                معلومات التعيين
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>اسم التعيين:</strong> {{ mapping.name }}</p>
                    <p><strong>اسم الصفحة:</strong> {{ mapping.sheet_name }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>معرف الجدول:</strong> {{ mapping.spreadsheet_id|truncatechars:30 }}</p>
                    <p><strong>صف العناوين:</strong> {{ mapping.header_row }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج تحديث التعيينات -->
    <form method="post" id="mappingForm">
        {% csrf_token %}
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-columns"></i>
                    تعيين الأعمدة
                </h5>
                <small class="text-muted">اختر الحقل المناسب لكل عمود من أعمدة Google Sheets</small>
            </div>
            <div class="card-body">
                <!-- مؤشر التحميل -->
                <div class="loading-spinner" id="loadingSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري جلب أعمدة الجدول...</p>
                </div>

                <!-- حاوية التعيينات -->
                <div class="mapping-container" id="mappingContainer" style="display: none;">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>

                <!-- أزرار الحفظ -->
                <div class="mt-4 text-center" id="saveButtons" style="display: none;">
                    <button type="submit" class="btn btn-save">
                        <i class="fas fa-save"></i>
                        حفظ التعيينات
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-3" onclick="resetMappings()">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- القيم الافتراضية للحقول المفقودة -->
    <div class="form-section mt-4">
        <h5 class="section-title">
            <i class="fas fa-sliders-h"></i>
            القيم الافتراضية
        </h5>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="default_customer_category" class="form-label">تصنيف العميل الافتراضي</label>
                    <select class="form-select" id="default_customer_category" name="default_customer_category">
                        <option value="">اختر تصنيف...</option>
                        {% for cat in customer_categories %}
                        <option value="{{ cat.id }}" {% if mapping.default_customer_category and mapping.default_customer_category.id == cat.id %}selected{% endif %}>{{ cat.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="default_branch" class="form-label">الفرع الافتراضي</label>
                    <select class="form-select" id="default_branch" name="default_branch">
                        <option value="">اختر فرع...</option>
                        {% for branch in branches %}
                        <option value="{{ branch.id }}" {% if mapping.default_branch and mapping.default_branch.id == branch.id %}selected{% endif %}>{{ branch.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// بيانات الحقول المتاحة
const fieldTypes = {{ field_types_json|safe }};

// جلب أعمدة الجدول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadSheetColumns();
});

function loadSheetColumns() {
    const loadingSpinner = document.getElementById('loadingSpinner');
    const mappingContainer = document.getElementById('mappingContainer');
    const saveButtons = document.getElementById('saveButtons');
    
    loadingSpinner.style.display = 'block';
    
    // جلب أعمدة الجدول
    fetch(`/odoo-db-manager/advanced-sync/api/sheet-columns/?spreadsheet_id={{ mapping.spreadsheet_id }}&sheet_name={{ mapping.sheet_name }}&header_row={{ mapping.header_row }}`)
        .then(response => response.json())
        .then(data => {
            loadingSpinner.style.display = 'none';
            
            if (data.success) {
                displayColumnMappings(data.headers);
                mappingContainer.style.display = 'block';
                saveButtons.style.display = 'block';
            } else {
                Swal.fire({
                    title: 'خطأ!',
                    text: data.error || 'فشل في جلب أعمدة الجدول',
                    icon: 'error',
                    customClass: {
                        container: 'swal-rtl'
                    }
                });
            }
        })
        .catch(error => {
            loadingSpinner.style.display = 'none';
            console.error('Error:', error);
            Swal.fire({
                title: 'خطأ!',
                text: 'حدث خطأ في الاتصال',
                icon: 'error',
                customClass: {
                    container: 'swal-rtl'
                }
            });
        });
}

function displayColumnMappings(headers) {
    const container = document.getElementById('mappingContainer');
    const existingMappings = {{ mapping.column_mappings|safe }};
    
    let html = '';
    
    headers.forEach((header, index) => {
        const columnName = header.original || header;
        const displayName = header.display || header.translated || header;
        const currentMapping = existingMappings[columnName] || '';
        
        html += `
            <div class="column-mapping-row">
                <div class="column-name arabic-text" title="${columnName}">
                    ${displayName}
                </div>
                <div class="field-select">
                    <select class="form-select" name="column_${columnName}" id="column_${index}">
                        <option value="">-- اختر الحقل --</option>
                        <option value="ignore" ${currentMapping === 'ignore' ? 'selected' : ''}>تجاهل هذا العمود</option>
        `;
        
        fieldTypes.forEach(([value, label]) => {
            const selected = currentMapping === value ? 'selected' : '';
            html += `<option value="${value}" ${selected}>${label}</option>`;
        });
        
        html += `
                    </select>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function resetMappings() {
    Swal.fire({
        title: 'إعادة تعيين التعيينات',
        text: 'هل تريد إعادة تعيين جميع التعيينات؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، أعد التعيين',
        cancelButtonText: 'إلغاء',
        customClass: {
            container: 'swal-rtl'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // إعادة تعيين جميع القوائم المنسدلة
            const selects = document.querySelectorAll('select[name^="column_"]');
            selects.forEach(select => {
                select.value = '';
            });
            
            Swal.fire({
                title: 'تم!',
                text: 'تم إعادة تعيين التعيينات',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false,
                customClass: {
                    container: 'swal-rtl'
                }
            });
        }
    });
}

// معالجة إرسال النموذج
document.getElementById('mappingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من وجود تعيين واحد على الأقل
    const selects = document.querySelectorAll('select[name^="column_"]');
    let hasMapping = false;
    
    selects.forEach(select => {
        if (select.value && select.value !== 'ignore') {
            hasMapping = true;
        }
    });
    
    if (!hasMapping) {
        Swal.fire({
            title: 'تنبيه!',
            text: 'يجب تعيين عمود واحد على الأقل',
            icon: 'warning',
            customClass: {
                container: 'swal-rtl'
            }
        });
        return;
    }
    
    // إظهار مؤشر التحميل
    Swal.fire({
        title: 'جاري الحفظ...',
        text: 'يرجى الانتظار',
        icon: 'info',
        allowOutsideClick: false,
        showConfirmButton: false,
        customClass: {
            container: 'swal-rtl'
        }
    });
    
    // إرسال النموذج
    this.submit();
});
</script>
{% endblock %}
