{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'odoo_db_manager/css/import.css' %}">
<style>
.log-card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.log-card:hover {
    transform: translateY(-2px);
}

.status-badge {
    font-size: 0.9em;
    padding: 0.5em 1em;
}

.status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-partial {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-failed {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 0.25rem;
}

.stat-number {
    font-size: 1.5em;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    font-size: 0.8em;
    color: #6c757d;
    margin-top: 0.25rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-history text-primary me-2"></i>
                    {{ title }}
                </h1>
                
                <div>
                    <a href="{% url 'odoo_db_manager:google_import_dashboard' %}" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>

            {% if logs %}
                <div class="row">
                    {% for log in logs %}
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card log-card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-file-alt me-2"></i>
                                    {{ log.sheet_name }}
                                </h5>
                                
                                {% if log.status == 'success' %}
                                    <span class="badge status-badge status-success">
                                        <i class="fas fa-check-circle me-1"></i>
                                        نجح
                                    </span>
                                {% elif log.status == 'partial' %}
                                    <span class="badge status-badge status-partial">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        جزئي
                                    </span>
                                {% else %}
                                    <span class="badge status-badge status-failed">
                                        <i class="fas fa-times-circle me-1"></i>
                                        فشل
                                    </span>
                                {% endif %}
                            </div>
                            
                            <div class="card-body">
                                <div class="text-muted mb-2">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ log.created_at|date:"d/m/Y - H:i" }}
                                </div>
                                
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="stat-number text-primary">{{ log.total_records }}</div>
                                        <div class="stat-label">إجمالي</div>
                                    </div>
                                    
                                    <div class="stat-item">
                                        <div class="stat-number text-success">{{ log.imported_records }}</div>
                                        <div class="stat-label">جديد</div>
                                    </div>
                                    
                                    <div class="stat-item">
                                        <div class="stat-number text-info">{{ log.updated_records }}</div>
                                        <div class="stat-label">محدث</div>
                                    </div>
                                    
                                    <div class="stat-item">
                                        <div class="stat-number text-danger">{{ log.failed_records }}</div>
                                        <div class="stat-label">فشل</div>
                                    </div>
                                </div>
                                
                                {% if log.error_details %}
                                <div class="mt-3">
                                    <details>
                                        <summary class="text-danger" style="cursor: pointer;">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            عرض الأخطاء
                                        </summary>
                                        <div class="mt-2 p-2 bg-light rounded">
                                            <pre class="mb-0" style="white-space: pre-wrap; font-size: 0.9em;">{{ log.error_details }}</pre>
                                        </div>
                                    </details>
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ log.user.get_full_name|default:log.user.username }}
                                    </small>
                                    
                                    {% if log.clear_existing %}
                                        <small class="text-warning">
                                            <i class="fas fa-trash me-1"></i>
                                            تم مسح البيانات السابقة
                                        </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- إحصائيات عامة -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    إحصائيات عامة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="border-end">
                                            <h3 class="text-primary">{{ logs.count }}</h3>
                                            <p class="text-muted mb-0">عمليات الاستيراد</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border-end">
                                            <h3 class="text-success">
                                                {{ logs|length|add:"-"|add:logs.count }}
                                            </h3>
                                            <p class="text-muted mb-0">عمليات ناجحة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border-end">
                                            <h3 class="text-info">
                                                {% for log in logs %}
                                                    {% if forloop.first %}{{ log.imported_records|add:log.updated_records }}{% endif %}
                                                {% endfor %}
                                            </h3>
                                            <p class="text-muted mb-0">إجمالي السجلات المعالجة</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <h3 class="text-warning">
                                            {% for log in logs %}
                                                {% if forloop.first %}{{ log.failed_records }}{% endif %}
                                            {% endfor %}
                                        </h3>
                                        <p class="text-muted mb-0">سجلات فشلت</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
            {% else %}
                <!-- لا توجد عمليات استيراد -->
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-history text-muted" style="font-size: 4rem;"></i>
                    </div>
                    <h4 class="text-muted">لا توجد عمليات استيراد بعد</h4>
                    <p class="text-muted mb-4">
                        لم تقم بأي عمليات استيراد حتى الآن. 
                        ابدأ بإنشاء عملية استيراد جديدة.
                    </p>
                    <a href="{% url 'odoo_db_manager:google_import_form' %}" 
                       class="btn btn-primary">
                        <i class="fas fa-plus"></i> بدء عملية استيراد جديدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
