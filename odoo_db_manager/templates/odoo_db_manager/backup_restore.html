{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'odoo_db_manager/css/style.css' %}">
{% endblock %}

{% block content %}
<div class="odoo-dashboard">
    <!-- شريط الأدوات العلوي -->
    <div class="odoo-toolbar">
        <div class="odoo-toolbar-left">
            <h1>{{ title }}</h1>
        </div>
        <div class="odoo-toolbar-right">
            <a href="{% url 'odoo_db_manager:backup_detail' backup.id %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة
            </a>
        </div>
    </div>

    <!-- نموذج استعادة النسخة الاحتياطية -->
    <div class="odoo-form">
        <div class="alert alert-warning">
            <h4 class="alert-heading">تحذير!</h4>
            <p>أنت على وشك استعادة النسخة الاحتياطية <strong>{{ backup.name }}</strong> لقاعدة البيانات <strong>{{ backup.database.name }}</strong>.</p>
            <p>سيتم استبدال جميع البيانات الحالية في قاعدة البيانات بالبيانات من النسخة الاحتياطية.</p>
            <p class="mb-0"><strong>تنبيه:</strong> هذا الإجراء لا يمكن التراجع عنه.</p>
        </div>

        <form method="post">
            {% csrf_token %}

            <div class="form-check mb-3">
                <input type="checkbox" class="form-check-input" id="confirm" name="confirm" required>
                <label class="form-check-label" for="confirm">أنا أفهم أن هذا الإجراء سيؤدي إلى استبدال جميع البيانات الحالية</label>
            </div>

            <div class="form-check mb-3">
                <input type="checkbox" class="form-check-input" id="clear_data" name="clear_data">
                <label class="form-check-label" for="clear_data">حذف البيانات القديمة قبل الاستعادة (موصى به)</label>
                <small class="form-text text-muted d-block mt-1">
                    يساعد هذا الخيار في تجنب تكرار البيانات وتضارب المعرفات. إذا كنت تواجه مشاكل في الاستعادة، جرب تفعيل هذا الخيار.
                </small>
            </div>

            <div class="alert alert-info">
                <h5>معلومات النسخة الاحتياطية:</h5>
                <ul>
                    <li><strong>نوع النسخة الاحتياطية:</strong>
                        {% if backup.backup_type == 'customers' %}
                            بيانات العملاء
                        {% elif backup.backup_type == 'users' %}
                            بيانات المستخدمين
                        {% elif backup.backup_type == 'settings' %}
                            إعدادات النظام
                        {% elif backup.backup_type == 'full' %}
                            كل البيانات
                        {% else %}
                            {{ backup.backup_type }}
                        {% endif %}
                    </li>
                    <li><strong>تاريخ الإنشاء:</strong> {{ backup.created_at|date:"Y-m-d H:i" }}</li>
                    <li><strong>حجم الملف:</strong> {{ backup.size_display }}</li>
                </ul>
            </div>

            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">استعادة النسخة الاحتياطية</button>
                <a href="{% url 'odoo_db_manager:backup_detail' backup.id %}" class="btn btn-secondary">إلغاء</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
