{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>{{ title }}</h2>
        <div>
            <a href="{% url 'odoo_db_manager:schedule_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> عودة للجدولات
            </a>
        </div>
    </div>

    {% if error %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>خطأ:</strong> {{ error }}
    </div>
    {% endif %}

    <!-- حالة المجدول -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs"></i> حالة المجدول
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-6">
                            <strong>المجدول متاح:</strong>
                        </div>
                        <div class="col-sm-6">
                            {% if scheduler_available %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i> نعم
                                </span>
                            {% else %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-times"></i> لا
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-sm-6">
                            <strong>المجدول يعمل:</strong>
                        </div>
                        <div class="col-sm-6">
                            {% if scheduler_running %}
                                <span class="badge bg-success">
                                    <i class="fas fa-play"></i> يعمل
                                </span>
                            {% else %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-pause"></i> متوقف
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-sm-6">
                            <strong>عدد المهام:</strong>
                        </div>
                        <div class="col-sm-6">
                            <span class="badge bg-info">{{ scheduler_jobs }}</span>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <form method="post" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="restart_scheduler">
                                <button type="submit" class="btn btn-warning btn-sm">
                                    <i class="fas fa-redo"></i> إعادة تشغيل المجدول
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt"></i> الجدولات النشطة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-6">
                            <strong>عدد الجدولات:</strong>
                        </div>
                        <div class="col-sm-6">
                            <span class="badge bg-primary">{{ active_schedules_count }}</span>
                        </div>
                    </div>
                    
                    {% if active_schedules %}
                    <div class="mb-3">
                        <strong>الجدولات:</strong>
                        <ul class="list-unstyled mt-2">
                            {% for schedule in active_schedules %}
                            <li class="mb-1">
                                <a href="{% url 'odoo_db_manager:schedule_detail' schedule.pk %}" class="text-decoration-none">
                                    <i class="fas fa-calendar"></i> {{ schedule.name }}
                                </a>
                                <small class="text-muted">({{ schedule.get_frequency_display }})</small>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-12">
                            <form method="post" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="run_manual_backup">
                                <button type="submit" class="btn btn-success btn-sm">
                                    <i class="fas fa-play"></i> تشغيل النسخ الاحتياطية يدوياً
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- النسخ الاحتياطية الأخيرة -->
    {% if recent_scheduled_backups %}
    <div class="card">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-history"></i> آخر النسخ الاحتياطية المجدولة
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم النسخة</th>
                            <th>قاعدة البيانات</th>
                            <th>النوع</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الحجم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for backup in recent_scheduled_backups %}
                        <tr>
                            <td>{{ backup.name }}</td>
                            <td>{{ backup.database.name }}</td>
                            <td>
                                <span class="badge bg-info">{{ backup.get_backup_type_display }}</span>
                            </td>
                            <td>{{ backup.created_at|date:"Y-m-d H:i" }}</td>
                            <td>{{ backup.file_size|filesizeformat }}</td>
                            <td>
                                <a href="{% url 'odoo_db_manager:backup_detail' backup.pk %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>تنبيه:</strong> لا توجد نسخ احتياطية مجدولة حتى الآن.
        {% if not scheduler_running %}
        <br><small>قد يكون السبب أن المجدول لا يعمل في بيئة الإنتاج الحالية.</small>
        {% endif %}
    </div>
    {% endif %}

    <!-- معلومات إضافية -->
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-info-circle"></i> معلومات مهمة
            </h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h6><i class="fas fa-lightbulb"></i> نصائح لحل مشاكل النسخ الاحتياطية:</h6>
                <ul class="mb-0">
                    <li><strong>في بيئة الإنتاج:</strong> قد لا يعمل المجدول التلقائي بسبب قيود الخادم</li>
                    <li><strong>الحل البديل:</strong> استخدم زر "تشغيل النسخ الاحتياطية يدوياً" بانتظام</li>
                    <li><strong>أو استخدم الأمر:</strong> <code>python manage.py run_scheduled_backups</code></li>
                    <li><strong>للتشغيل الإجباري:</strong> <code>python manage.py run_scheduled_backups --force</code></li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
