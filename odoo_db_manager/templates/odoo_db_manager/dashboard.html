{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'odoo_db_manager/css/style.css' %}">
<style>
    /* أنماط الأيقونات */
    .btn-link {
        border: none;
        background: none;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .btn-link:hover {
        transform: scale(1.1);
        text-decoration: none;
    }

    .btn-link i {
        transition: all 0.3s ease;
    }

    .btn-link:hover i {
        filter: brightness(1.2);
    }

    /* تحسين عرض SweetAlert */
    .swal-wide {
        width: 600px !important;
    }

    .swal-wide .swal2-html-container {
        text-align: right;
        direction: rtl;
    }

    .swal-wide .alert {
        margin-bottom: 15px;
        padding: 12px;
        border-radius: 6px;
    }

    .swal-wide ul {
        list-style: none;
        padding-right: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="odoo-dashboard">
    <!-- شريط الأدوات العلوي -->
    <div class="odoo-toolbar">
        <div class="odoo-toolbar-left">
            <h1>{{ title }}</h1>
        </div>
        <div class="odoo-toolbar-right">
            <a href="{% url 'odoo_db_manager:database_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إنشاء قاعدة بيانات
            </a>
            <a href="{% url 'odoo_db_manager:backup_create' %}" class="btn btn-success">
                <i class="fas fa-save"></i> نسخة احتياطية
            </a>
            <a href="{% url 'odoo_db_manager:schedule_list' %}" class="btn btn-warning">
                <i class="fas fa-clock"></i> جدولة النسخ الاحتياطية
            </a>
            <a href="{% url 'odoo_db_manager:backup_upload' %}" class="btn btn-info">
                <i class="fas fa-upload"></i> استعادة من ملف
            </a>
            <button onclick="refreshConnectionStatus()" class="btn btn-secondary">
                <i class="fas fa-sync-alt"></i> تحديث حالة الاتصال
            </button>
            <a href="{% url 'odoo_db_manager:google_drive_settings' %}" class="btn btn-outline-primary">
                <i class="fab fa-google-drive"></i> إعدادات Google Drive
            </a>            <a href="{% url 'odoo_db_manager:google_sync' %}" class="btn btn-outline-success">
                <i class="fas fa-sync-alt"></i> مزامنة غوغل
            </a>
            <a href="{% url 'odoo_db_manager:advanced_sync_dashboard' %}" class="btn btn-outline-warning">
                <i class="fas fa-sync-alt"></i> المزامنة المتقدمة
            </a>
        </div>
    </div>

    <!-- قائمة قواعد البيانات -->
    <div class="odoo-list-view">
        <table class="table">
            <thead>
                <tr>
                    <th>اسم قاعدة البيانات</th>
                    <th>النوع</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for db in databases %}
                <tr>
                    <td>
                        <strong>{{ db.name }}</strong>
                        {% if db.connection_info.NAME %}
                            <small class="text-muted d-block">{{ db.connection_info.NAME }}</small>
                        {% endif %}
                    </td>
                    <td>{{ db.get_db_type_display }}</td>
                    <td>
                        <div class="d-flex align-items-center">
                            {% if db.is_active %}
                            <span class="badge bg-primary me-1">نشطة</span>
                            {% endif %}

                            {% if db.status %}
                            <span class="badge bg-success" title="متصلة">
                                <i class="fas fa-plug"></i>
                            </span>
                            {% else %}
                            <span class="badge bg-danger" title="غير متصلة">
                                <i class="fas fa-times-circle"></i>
                            </span>
                            {% endif %}
                        </div>
                    </td>
                    <td>{{ db.created_at|date:"Y-m-d H:i" }}</td>
                    <td>
                        <div class="d-flex justify-content-center gap-1">
                            <a href="{% url 'odoo_db_manager:database_detail' db.id %}" class="btn btn-link text-info p-1" title="عرض التفاصيل">
                                <i class="fas fa-eye fa-lg"></i>
                            </a>
                            <a href="{% url 'odoo_db_manager:backup_create_for_database' db.id %}" class="btn btn-link text-success p-1" title="إنشاء نسخة احتياطية">
                                <i class="fas fa-download fa-lg"></i>
                            </a>
                            <a href="{% url 'odoo_db_manager:schedule_create_for_database' db.id %}" class="btn btn-link text-warning p-1" title="جدولة النسخ الاحتياطية">
                                <i class="fas fa-clock fa-lg"></i>
                            </a>
                            {% if not db.is_active %}
                            <button type="button" onclick="confirmActivateDatabase('{% url 'odoo_db_manager:activate_database' db.id %}', '{{ db.name }}'); return false;" class="btn btn-link text-primary p-1" title="تنشيط قاعدة البيانات">
                                <i class="fas fa-power-off fa-lg"></i>
                            </button>
                            {% endif %}
                            <a href="{% url 'odoo_db_manager:delete_database' db.id %}" class="btn btn-link text-danger p-1" title="حذف قاعدة البيانات">
                                <i class="fas fa-trash fa-lg"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="text-center">لا توجد قواعد بيانات</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="odoo-stats">
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">قواعد البيانات</h5>
                        <p class="card-text display-4">{{ databases.count }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">النسخ الاحتياطية</h5>
                        <p class="card-text display-4">{{ backups.count }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">المساحة المستخدمة</h5>
                        <p class="card-text display-4">{{ total_size_display }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">آخر نسخة احتياطية</h5>
                        <p class="card-text">{{ last_backup.created_at|date:"Y-m-d H:i"|default:"لا يوجد" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- النسخ الاحتياطية الأخيرة -->
    <div class="mt-4">
        <h3>النسخ الاحتياطية الأخيرة</h3>
        <div class="odoo-list-view">
            <table class="table">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>قاعدة البيانات</th>
                        <th>الحجم</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for backup in backups %}
                    <tr>
                        <td>{{ backup.name }}</td>
                        <td>{{ backup.database.name }}</td>
                        <td>{{ backup.size_display }}</td>
                        <td>{{ backup.created_at|date:"Y-m-d H:i" }}</td>
                        <td>
                            <div class="d-flex justify-content-center gap-1">
                                <a href="{% url 'odoo_db_manager:backup_detail' backup.id %}" class="btn btn-link text-info p-1" title="عرض التفاصيل">
                                    <i class="fas fa-eye fa-lg"></i>
                                </a>
                                <a href="{% url 'odoo_db_manager:backup_download' backup.id %}" class="btn btn-link text-success p-1" title="تنزيل">
                                    <i class="fas fa-download fa-lg"></i>
                                </a>
                                <a href="{% url 'odoo_db_manager:backup_restore' backup.id %}" class="btn btn-link text-warning p-1" title="استعادة">
                                    <i class="fas fa-undo fa-lg"></i>
                                </a>
                                <a href="{% url 'odoo_db_manager:backup_delete' backup.id %}" class="btn btn-link text-danger p-1" title="حذف">
                                    <i class="fas fa-trash fa-lg"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center">لا توجد نسخ احتياطية</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- قواعد البيانات المكتشفة في PostgreSQL -->
    {% if discovered_databases %}
    <div class="odoo-section mt-4">
        <h3>قواعد البيانات الموجودة في PostgreSQL</h3>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>اسم قاعدة البيانات</th>
                        <th>المالك</th>
                        <th>الترميز</th>
                        <th>الحجم</th>
                        <th>مسجلة في النظام</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for db_info in discovered_databases %}
                    <tr>
                        <td><strong>{{ db_info.name }}</strong></td>
                        <td>{{ db_info.owner|default:"غير محدد" }}</td>
                        <td>{{ db_info.encoding|default:"UTF8" }}</td>
                        <td>{{ db_info.size|default:"غير محدد" }}</td>
                        <td>
                            {% if db_info.registered %}
                                <span class="badge bg-success">مسجلة</span>
                            {% else %}
                                <span class="badge bg-warning">غير مسجلة</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if not db_info.registered %}
                                <button class="btn btn-sm btn-primary" onclick="registerDatabase('{{ db_info.name }}')">
                                    <i class="fas fa-plus"></i> تسجيل
                                </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // عرض رسالة نجاح تنشيط قاعدة البيانات إذا كان ضروريًا
    {% if show_activation_success %}
    document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
            title: 'تم تنشيط قاعدة البيانات بنجاح!',
            html: `
                <div style="text-align: right; direction: rtl;">
                    <p>تم تنشيط قاعدة البيانات <strong>{{ activated_db_name }}</strong> بنجاح.</p>

                    {% if created_default_user %}
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-user-plus"></i>
                        تم إنشاء مستخدم افتراضي:
                        <ul class="mt-2">
                            <li><strong>اسم المستخدم:</strong> admin</li>
                            <li><strong>كلمة المرور:</strong> admin</li>
                        </ul>
                    </div>
                    {% endif %}

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i>
                        يرجى إعادة تشغيل السيرفر لتطبيق التغييرات.
                    </div>
                </div>
            `,
            icon: 'success',
            confirmButtonText: 'تم',
            customClass: {
                popup: 'rtl-popup',
                title: 'rtl-title',
                content: 'rtl-content'
            }        });
    });
    {% endif %}

    // دالة تأكيد تنشيط قاعدة البيانات باستخدام SweetAlert
    function confirmActivateDatabase(url, dbName) {
        console.log('confirmActivateDatabase called with:', url, dbName); // للتشخيص

        // التحقق من وجود SweetAlert
        if (typeof Swal === 'undefined') {
            console.error('SweetAlert2 is not loaded!');
            alert('خطأ: SweetAlert2 غير محمل');
            return false;
        }

        console.log('SweetAlert2 is loaded, showing confirmation dialog...');

        Swal.fire({
            title: 'تنشيط قاعدة البيانات',
            html: `هل أنت متأكد من تنشيط قاعدة البيانات <strong>${dbName}</strong>؟<br><br>
                  <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    ملاحظة: سيتم إعادة تشغيل السيرفر لتطبيق التغييرات.
                  </div>`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، قم بالتنشيط',
            cancelButtonText: 'إلغاء',
            customClass: {
                popup: 'rtl-popup',
                title: 'rtl-title',
                content: 'rtl-content'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('User confirmed activation, sending request to:', url); // للتتبع

                // عرض مؤشر التحميل
                Swal.fire({
                    title: 'جاري تنشيط قاعدة البيانات...',
                    html: 'يرجى الانتظار...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }                });

                // إرسال طلب AJAX للتنشيط
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: 'action=activate'  // إضافة body للطلب
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    // التحقق من Content-Type
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return response.json();
                    } else {
                        // إذا لم يكن JSON، قراءة كـ text
                        return response.text().then(text => {
                            throw new Error(`Expected JSON but got: ${text.substring(0, 100)}...`);
                        });
                    }
                })
                .then(data => {
                    console.log('Response data:', data); // للتتبع

                    // إخفاء مؤشر التحميل
                    Swal.close();
                      if (data.success) {
                        // بناء محتوى الرسالة
                        let htmlContent = `
                            <div class="text-center">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    تم تنشيط قاعدة البيانات "${data.database_name}" بنجاح!
                                </div>
                        `;

                        if (data.created_default_user) {
                            htmlContent += `
                                <div class="alert alert-info">
                                    <i class="fas fa-user-plus"></i>
                                    <strong>تم إنشاء مستخدم افتراضي:</strong><br>
                                    اسم المستخدم: <code>admin</code><br>
                                    كلمة المرور: <code>admin123</code>
                                </div>
                            `;                        }

                        if (data.requires_restart) {
                            htmlContent += `
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    يرجى إعادة تشغيل السيرفر لتطبيق التغييرات.
                                </div>
                            `;
                        } else {
                            htmlContent += `
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    تم تطبيق التغييرات مباشرة. يمكنك الآن استخدام قاعدة البيانات الجديدة.
                                </div>
                            `;                        }

                        htmlContent += '</div>';

                        // عرض SweetAlert مع المحتوى المنسق
                        Swal.fire({
                            title: 'تم التنشيط بنجاح!',
                            html: htmlContent,
                            icon: 'success',
                            confirmButtonText: 'تم',
                            confirmButtonColor: '#28a745',
                            width: '600px',
                            customClass: {
                                popup: 'rtl-popup swal-wide',
                                title: 'rtl-title',
                                content: 'rtl-content'
                            }
                        }).then(() => {
                            // إعادة تحميل الصفحة لتحديث البيانات
                            window.location.reload();
                        });
                    } else {
                        Swal.fire({
                            title: 'خطأ في التنشيط',
                            text: data.message,
                            icon: 'error',
                            confirmButtonText: 'تم',
                            customClass: {
                                popup: 'rtl-popup',
                                title: 'rtl-title',
                                content: 'rtl-content'
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء تنشيط قاعدة البيانات. يرجى المحاولة مرة أخرى.',
                        icon: 'error',                        confirmButtonText: 'تم',
                        customClass: {
                            popup: 'rtl-popup',
                            title: 'rtl-title',
                            content: 'rtl-content'
                        }
                    });
                });
            }
        });

        return false; // منع السلوك الافتراضي للرابط
    }

    // دالة تسجيل قاعدة بيانات جديدة
    function registerDatabase(dbName) {
        Swal.fire({
            title: 'تسجيل قاعدة بيانات جديدة',
            html: `هل أنت متأكد من تسجيل قاعدة بيانات جديدة باسم <strong>${dbName}</strong>؟`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، قم بالتسجيل',
            cancelButtonText: 'إلغاء',
            customClass: {
                popup: 'rtl-popup',
                title: 'rtl-title',
                content: 'rtl-content'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // عرض مؤشر التحميل
                Swal.fire({
                    title: 'جاري تسجيل قاعدة البيانات...',
                    html: 'يرجى الانتظار...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                  // إرسال طلب تسجيل قاعدة البيانات إلى الخادم
                fetch('{% url "odoo_db_manager:database_register" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify({ name: dbName })
                })
                .then(response => response.json())
                .then(data => {
                    // إخفاء مؤشر التحميل
                    Swal.close();

                    if (data.success) {
                        // إعادة تحميل الصفحة لعرض قاعدة البيانات المسجلة حديثًا
                        location.reload();
                    } else {
                        // عرض رسالة خطأ
                        Swal.fire({
                            title: 'خطأ',
                            text: data.error,
                            icon: 'error',
                            confirmButtonText: 'تم',
                            customClass: {
                                popup: 'rtl-popup',
                                title: 'rtl-title',
                                content: 'rtl-content'
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء تسجيل قاعدة البيانات. يرجى المحاولة مرة أخرى.',
                        icon: 'error',
                        confirmButtonText: 'تم',
                        customClass: {
                            popup: 'rtl-popup',
                            title: 'rtl-title',
                            content: 'rtl-content'
                        }
                    });
                });
            }
        });
    }

    // دالة لتحديث حالة الاتصال لجميع قواعد البيانات
    function refreshConnectionStatus() {
        // عرض مؤشر التحميل
        Swal.fire({
            title: 'جاري تحديث حالة الاتصال...',
            html: 'يرجى الانتظار...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // إرسال طلب لتحديث حالة الاتصال إلى الخادم
        fetch('{% url "odoo_db_manager:database_refresh_status" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            // إخفاء مؤشر التحميل
            Swal.close();

            if (data.success) {
                // إعادة تحميل الصفحة لتحديث حالة الاتصال
                location.reload();
            } else {
                // عرض رسالة خطأ
                Swal.fire({
                    title: 'خطأ',
                    text: data.error,
                    icon: 'error',
                    confirmButtonText: 'تم',
                    customClass: {
                        popup: 'rtl-popup',
                        title: 'rtl-title',
                        content: 'rtl-content'
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                title: 'خطأ',
                text: 'حدث خطأ أثناء تحديث حالة الاتصال. يرجى المحاولة مرة أخرى.',
                icon: 'error',
                confirmButtonText: 'تم',
                customClass: {
                    popup: 'rtl-popup',
                    title: 'rtl-title',
                    content: 'rtl-content'
                }
            });
        });
    }
</script>
{% endblock %}
