{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'odoo_db_manager/css/style.css' %}">
{% endblock %}

{% block content %}
<div class="odoo-dashboard">
    <!-- شريط الأدوات العلوي -->
    <div class="odoo-toolbar">
        <div class="odoo-toolbar-left">
            <h1>{{ title }}</h1>
        </div>
        <div class="odoo-toolbar-right">
            <a href="{% url 'odoo_db_manager:dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة
            </a>
        </div>
    </div>

    <!-- نموذج إنشاء نسخة احتياطية -->
    <div class="odoo-form">
        <form method="post">
            {% csrf_token %}

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group mb-3">
                        <label for="database_id">قاعدة البيانات</label>
                        <select class="form-control" id="database_id" name="database_id" required {% if database %}disabled{% endif %}>
                            <option value="">-- اختر قاعدة البيانات --</option>
                            {% for db in databases %}
                            <option value="{{ db.id }}" {% if database and database.id == db.id %}selected{% endif %}>{{ db.name }}</option>
                            {% endfor %}
                        </select>
                        {% if database %}
                        <input type="hidden" name="database_id" value="{{ database.id }}">
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group mb-3">
                        <label for="name">اسم النسخة الاحتياطية</label>
                        <input type="text" class="form-control" id="name" name="name" placeholder="اترك فارغاً لاستخدام اسم تلقائي">
                        <small class="form-text text-muted">إذا تركت هذا الحقل فارغاً، سيتم إنشاء اسم تلقائي بناءً على اسم قاعدة البيانات والتاريخ.</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group mb-3">
                        <label for="backup_type">نوع النسخة الاحتياطية</label>
                        <select class="form-control" id="backup_type" name="backup_type" required>
                            {% for value, label in backup_types %}
                            <option value="{{ value }}" {% if value == 'full' %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">
                            <ul class="mt-2">
                                <li><strong>بيانات العملاء:</strong> تشمل معلومات العملاء وجهات الاتصال والعناوين فقط</li>
                                <li><strong>بيانات المستخدمين:</strong> تشمل حسابات المستخدمين والمجموعات والصلاحيات</li>
                                <li><strong>إعدادات النظام:</strong> تشمل إعدادات النظام الأساسية</li>
                                <li><strong>كل البيانات:</strong> تشمل جميع البيانات في النظام (الخيار الموصى به)</li>
                            </ul>
                        </small>
                    </div>
                </div>
            </div>

            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">إنشاء نسخة احتياطية</button>
                <a href="{% url 'odoo_db_manager:dashboard' %}" class="btn btn-secondary">إلغاء</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
