# Generated by Django 4.2.21 on 2025-07-02 13:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('odoo_db_manager', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='googledriveconfig',
            name='contracts_folder_id',
            field=models.CharField(blank=True, help_text='معرف المجلد في Google Drive لحفظ ملفات العقود', max_length=255, verbose_name='معرف مجلد العقود'),
        ),
        migrations.AddField(
            model_name='googledriveconfig',
            name='contracts_folder_name',
            field=models.CharField(blank=True, default='العقود - Contracts', help_text='اسم المجلد في Google Drive للعقود', max_length=255, verbose_name='اسم مجلد العقود'),
        ),
    ]
