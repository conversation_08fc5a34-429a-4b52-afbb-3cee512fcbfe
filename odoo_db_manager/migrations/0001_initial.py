# Generated by Django 4.2.21 on 2025-07-02 13:30

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Database',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم قاعدة البيانات')),
                ('db_type', models.CharField(choices=[('postgresql', 'PostgreSQL')], max_length=20, verbose_name='نوع قاعدة البيانات')),
                ('connection_info', models.JSONField(default=dict, verbose_name='معلومات الاتصال')),
                ('is_active', models.BooleanField(default=False, verbose_name='نشطة')),
                ('status', models.BooleanField(default=False, verbose_name='حالة الاتصال')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='رسالة الخطأ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قاعدة بيانات',
                'verbose_name_plural': 'قواعد البيانات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleSheetMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم التعيين')),
                ('spreadsheet_id', models.CharField(max_length=500, verbose_name='معرف جدول Google')),
                ('sheet_name', models.CharField(default='Sheet1', max_length=200, verbose_name='اسم الورقة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('header_row', models.PositiveIntegerField(default=1, verbose_name='صف العناوين')),
                ('start_row', models.PositiveIntegerField(default=2, verbose_name='صف البداية')),
                ('last_row_processed', models.PositiveIntegerField(blank=True, null=True, verbose_name='آخر صف تم معالجته')),
                ('column_mappings', models.JSONField(default=dict, verbose_name='تعيينات الأعمدة')),
                ('auto_create_customers', models.BooleanField(default=True, verbose_name='إنشاء عملاء تلقائياً')),
                ('auto_create_orders', models.BooleanField(default=True, verbose_name='إنشاء طلبات تلقائياً')),
                ('auto_create_inspections', models.BooleanField(default=False, verbose_name='إنشاء معاينات تلقائياً')),
                ('auto_create_installations', models.BooleanField(default=False, verbose_name='إنشاء تركيبات تلقائياً')),
                ('update_existing', models.BooleanField(default=True, verbose_name='تحديث الموجود')),
                ('conflict_resolution', models.CharField(choices=[('skip', 'تجاهل التعارضات'), ('overwrite', 'الكتابة فوق البيانات الموجودة'), ('manual', 'الحل اليدوي للتعارضات')], default='manual', max_length=20, verbose_name='حل التعارضات')),
                ('enable_reverse_sync', models.BooleanField(default=False, verbose_name='تمكين المزامنة العكسية')),
                ('reverse_sync_fields', models.JSONField(default=list, verbose_name='حقول المزامنة العكسية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('last_sync', models.DateTimeField(blank=True, null=True, verbose_name='آخر مزامنة')),
                ('row_filter_conditions', models.JSONField(blank=True, default=dict, verbose_name='شروط فلترة الصفوف')),
                ('data_validation_rules', models.JSONField(blank=True, default=dict, verbose_name='قواعد التحقق من البيانات')),
                ('default_customer_type', models.CharField(blank=True, choices=[('retail', 'أفراد'), ('wholesale', 'جملة'), ('corporate', 'شركات')], max_length=20, null=True, verbose_name='نوع العميل الافتراضي')),
                ('use_current_date_as_created', models.BooleanField(default=False, verbose_name='استخدام التاريخ الحالي كتاريخ الإضافة')),
                ('default_branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounts.branch', verbose_name='الفرع الافتراضي')),
                ('default_customer_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customers.customercategory', verbose_name='تصنيف العميل الافتراضي')),
            ],
            options={
                'verbose_name': 'تعيين Google Sheets',
                'verbose_name_plural': 'تعيينات Google Sheets',
                'db_table': 'google_sheet_mapping',
            },
        ),
        migrations.CreateModel(
            name='GoogleSyncConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('spreadsheet_id', models.CharField(max_length=255, verbose_name='معرف جدول البيانات')),
                ('credentials_file', models.FileField(upload_to='google_credentials/', verbose_name='ملف بيانات الاعتماد')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('last_sync', models.DateTimeField(blank=True, null=True, verbose_name='آخر مزامنة')),
                ('sync_frequency', models.IntegerField(default=24, verbose_name='تكرار المزامنة (بالساعات)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('sync_databases', models.BooleanField(default=True, verbose_name='مزامنة قواعد البيانات')),
                ('sync_users', models.BooleanField(default=True, verbose_name='مزامنة المستخدمين')),
                ('sync_customers', models.BooleanField(default=True, verbose_name='مزامنة العملاء')),
                ('sync_orders', models.BooleanField(default=True, verbose_name='مزامنة الطلبات')),
                ('sync_products', models.BooleanField(default=True, verbose_name='مزامنة المنتجات')),
                ('sync_settings', models.BooleanField(default=True, verbose_name='مزامنة الإعدادات')),
                ('sync_inspections', models.BooleanField(default=True, verbose_name='مزامنة المعاينات')),
            ],
            options={
                'verbose_name': 'إعداد مزامنة غوغل',
                'verbose_name_plural': 'إعدادات مزامنة غوغل',
            },
        ),
        migrations.CreateModel(
            name='ImportLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sheet_name', models.CharField(max_length=100, verbose_name='اسم الجدول')),
                ('total_records', models.IntegerField(default=0, verbose_name='إجمالي السجلات')),
                ('imported_records', models.IntegerField(default=0, verbose_name='السجلات المستوردة')),
                ('updated_records', models.IntegerField(default=0, verbose_name='السجلات المحدثة')),
                ('failed_records', models.IntegerField(default=0, verbose_name='السجلات الفاشلة')),
                ('clear_existing', models.BooleanField(default=False, verbose_name='حذف البيانات القديمة')),
                ('status', models.CharField(choices=[('success', 'نجح'), ('failed', 'فشل'), ('partial', 'جزئي')], default='success', max_length=20, verbose_name='الحالة')),
                ('error_details', models.TextField(blank=True, verbose_name='تفاصيل الأخطاء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_import_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_import_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل استيراد',
                'verbose_name_plural': 'سجلات الاستيراد',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleSyncTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_type', models.CharField(choices=[('import', 'استيراد من Google Sheets'), ('export', 'تصدير إلى Google Sheets'), ('sync_bidirectional', 'مزامنة ثنائية الاتجاه')], max_length=20, verbose_name='نوع المهمة')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('running', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('failed', 'فشلت'), ('cancelled', 'ملغية')], default='pending', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البداية')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('result', models.JSONField(blank=True, null=True, verbose_name='النتيجة')),
                ('rows_processed', models.PositiveIntegerField(default=0, verbose_name='الصفوف المعالجة')),
                ('rows_successful', models.PositiveIntegerField(default=0, verbose_name='الصفوف الناجحة')),
                ('rows_failed', models.PositiveIntegerField(default=0, verbose_name='الصفوف الفاشلة')),
                ('task_parameters', models.JSONField(blank=True, default=dict, verbose_name='معاملات المهمة')),
                ('error_log', models.TextField(blank=True, verbose_name='سجل الأخطاء')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_sync_tasks', to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('mapping', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sync_tasks', to='odoo_db_manager.googlesheetmapping', verbose_name='التعيين')),
            ],
            options={
                'verbose_name': 'مهمة مزامنة',
                'verbose_name_plural': 'مهام المزامنة',
                'db_table': 'google_sync_task',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleSyncSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='جدولة افتراضية', max_length=200, verbose_name='اسم الجدولة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('frequency', models.CharField(choices=[('once', 'مرة واحدة'), ('daily', 'يومياً'), ('weekly', 'أسبوعياً'), ('monthly', 'شهرياً'), ('hourly', 'كل ساعة')], default='daily', max_length=20, verbose_name='التكرار')),
                ('scheduled_time', models.TimeField(default=datetime.time(0, 0), verbose_name='وقت التنفيذ')),
                ('next_run', models.DateTimeField(blank=True, null=True, verbose_name='التنفيذ القادم')),
                ('task_type', models.CharField(choices=[('import', 'استيراد من Google Sheets'), ('export', 'تصدير إلى Google Sheets'), ('sync_bidirectional', 'مزامنة ثنائية الاتجاه')], default='import', max_length=20, verbose_name='نوع المهمة')),
                ('task_parameters', models.JSONField(blank=True, default=dict, verbose_name='معاملات المهمة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('last_run', models.DateTimeField(blank=True, null=True, verbose_name='آخر تنفيذ')),
                ('total_runs', models.PositiveIntegerField(default=0, verbose_name='إجمالي التنفيذ')),
                ('successful_runs', models.PositiveIntegerField(default=0, verbose_name='التنفيذ الناجح')),
                ('failed_runs', models.PositiveIntegerField(default=0, verbose_name='التنفيذ الفاشل')),
                ('mapping', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='odoo_db_manager.googlesheetmapping', verbose_name='التعيين')),
            ],
            options={
                'verbose_name': 'جدولة مزامنة',
                'verbose_name_plural': 'جدولة المزامنة',
                'db_table': 'google_sync_schedule',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleSyncLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('success', 'نجاح'), ('error', 'خطأ'), ('warning', 'تحذير')], max_length=20, verbose_name='الحالة')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('details', models.JSONField(blank=True, default=dict, verbose_name='التفاصيل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='odoo_db_manager.googlesyncconfig')),
            ],
            options={
                'verbose_name': 'سجل مزامنة غوغل',
                'verbose_name_plural': 'سجلات مزامنة غوغل',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleSyncConflict',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('conflict_type', models.CharField(choices=[('duplicate_customer', 'عميل مكرر'), ('duplicate_order', 'طلب مكرر'), ('data_mismatch', 'عدم تطابق البيانات'), ('missing_reference', 'مرجع مفقود'), ('validation_error', 'خطأ في التحقق')], max_length=30, verbose_name='نوع التعارض')),
                ('row_number', models.PositiveIntegerField(default=1, verbose_name='رقم الصف')),
                ('sheet_data', models.JSONField(verbose_name='بيانات الجدول')),
                ('existing_data', models.JSONField(blank=True, null=True, verbose_name='البيانات الموجودة')),
                ('suggested_action', models.CharField(blank=True, max_length=100, verbose_name='الإجراء المقترح')),
                ('resolution_status', models.CharField(choices=[('pending', 'في الانتظار'), ('resolved', 'تم الحل'), ('ignored', 'متجاهل')], default='pending', max_length=20, verbose_name='حالة الحل')),
                ('resolution_action', models.CharField(blank=True, max_length=100, verbose_name='إجراء الحل')),
                ('resolution_notes', models.TextField(blank=True, verbose_name='ملاحظات الحل')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conflicts', to='odoo_db_manager.googlesynctask', verbose_name='المهمة')),
            ],
            options={
                'verbose_name': 'تعارض مزامنة',
                'verbose_name_plural': 'تعارضات المزامنة',
                'db_table': 'google_sync_conflict',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleDriveConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='إعدادات Google Drive', max_length=100, verbose_name='اسم الإعداد')),
                ('inspections_folder_id', models.CharField(blank=True, help_text='معرف المجلد في Google Drive لحفظ ملفات المعاينات', max_length=255, verbose_name='معرف مجلد المعاينات')),
                ('inspections_folder_name', models.CharField(blank=True, help_text='اسم المجلد في Google Drive', max_length=255, verbose_name='اسم مجلد المعاينات')),
                ('credentials_file', models.FileField(blank=True, help_text='ملف JSON من Google Cloud Console', null=True, upload_to='google_credentials/', verbose_name='ملف اعتماد Google')),
                ('filename_pattern', models.CharField(default='{customer}_{branch}_{date}_{order}', help_text='المتغيرات المتاحة: {customer}, {branch}, {date}, {order}', max_length=200, verbose_name='نمط تسمية الملفات')),
                ('is_active', models.BooleanField(default=True, verbose_name='مفعل')),
                ('last_test', models.DateTimeField(blank=True, null=True, verbose_name='آخر اختبار')),
                ('test_status', models.CharField(blank=True, max_length=50, verbose_name='حالة الاختبار')),
                ('test_message', models.TextField(blank=True, verbose_name='رسالة الاختبار')),
                ('total_uploads', models.IntegerField(default=0, verbose_name='إجمالي الرفعات')),
                ('last_upload', models.DateTimeField(blank=True, null=True, verbose_name='آخر رفع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'إعدادات Google Drive',
                'verbose_name_plural': 'إعدادات Google Drive',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BackupSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الجدولة')),
                ('backup_type', models.CharField(choices=[('customers', 'بيانات العملاء'), ('users', 'بيانات المستخدمين'), ('settings', 'إعدادات النظام'), ('full', 'كل البيانات')], default='full', max_length=20, verbose_name='نوع النسخة الاحتياطية')),
                ('frequency', models.CharField(choices=[('hourly', 'كل ساعة'), ('daily', 'يومياً'), ('weekly', 'أسبوعياً'), ('monthly', 'شهرياً')], default='daily', max_length=20, verbose_name='التكرار')),
                ('hour', models.IntegerField(default=0, help_text='0-23', verbose_name='الساعة')),
                ('minute', models.IntegerField(default=0, help_text='0-59', verbose_name='الدقيقة')),
                ('day_of_week', models.IntegerField(blank=True, choices=[(0, 'الاثنين'), (1, 'الثلاثاء'), (2, 'الأربعاء'), (3, 'الخميس'), (4, 'الجمعة'), (5, 'السبت'), (6, 'الأحد')], default=0, null=True, verbose_name='يوم الأسبوع')),
                ('day_of_month', models.IntegerField(blank=True, default=1, help_text='1-31', null=True, verbose_name='يوم الشهر')),
                ('max_backups', models.IntegerField(default=24, help_text='الحد الأقصى هو 24 نسخة', verbose_name='الحد الأقصى لعدد النسخ')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('last_run', models.DateTimeField(blank=True, null=True, verbose_name='آخر تشغيل')),
                ('next_run', models.DateTimeField(blank=True, null=True, verbose_name='التشغيل القادم')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='backup_schedules', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('database', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='backup_schedules', to='odoo_db_manager.database', verbose_name='قاعدة البيانات')),
            ],
            options={
                'verbose_name': 'جدولة النسخ الاحتياطية',
                'verbose_name_plural': 'جدولة النسخ الاحتياطية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Backup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النسخة الاحتياطية')),
                ('file_path', models.CharField(max_length=255, verbose_name='مسار الملف')),
                ('size', models.BigIntegerField(default=0, verbose_name='الحجم (بايت)')),
                ('backup_type', models.CharField(choices=[('customers', 'بيانات العملاء'), ('users', 'بيانات المستخدمين'), ('settings', 'إعدادات النظام'), ('full', 'كل البيانات')], default='full', max_length=20, verbose_name='نوع النسخة الاحتياطية')),
                ('is_scheduled', models.BooleanField(default=False, verbose_name='مجدولة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('database', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='backups', to='odoo_db_manager.database', verbose_name='قاعدة البيانات')),
            ],
            options={
                'verbose_name': 'نسخة احتياطية',
                'verbose_name_plural': 'النسخ الاحتياطية',
                'ordering': ['-created_at'],
            },
        ),
    ]
