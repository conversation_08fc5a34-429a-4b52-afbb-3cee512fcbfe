/**
 * Estilos para la interfaz de usuario de Odoo DB Manager
 * Mantiene la identidad visual de la aplicación mientras imita la interfaz de Odoo
 */

/* Estilos generales */
.odoo-dashboard {
    background-color: var(--light-bg);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.odoo-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--neutral);
}

.odoo-toolbar h1 {
    color: var(--dark-text);
    font-size: 1.8rem;
    margin: 0;
}

.odoo-list-view {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
}

.odoo-list-view table {
    margin-bottom: 0;
}

.odoo-list-view th {
    background-color: var(--primary);
    color: white;
    font-weight: 500;
    border: none;
}

.odoo-list-view tr:hover {
    background-color: var(--light-bg);
}

.odoo-stats {
    margin-top: 30px;
}

.odoo-stats .card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.odoo-stats .card:hover {
    transform: translateY(-5px);
}

.odoo-stats .card-title {
    color: var(--dark-text);
    font-size: 1rem;
    font-weight: 600;
}

.odoo-stats .card-text.display-4 {
    color: var(--primary);
    font-weight: 600;
}

/* Formularios */
.odoo-form {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
}

.odoo-form .form-group {
    margin-bottom: 20px;
}

.odoo-form label {
    font-weight: 600;
    color: var(--dark-text);
}

.odoo-form .btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.odoo-form .btn-primary:hover {
    background-color: var(--accent);
    border-color: var(--accent);
}

/* Detalles */
.odoo-detail {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
}

.odoo-detail .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--neutral);
}

.odoo-detail .detail-header h2 {
    color: var(--dark-text);
    font-size: 1.5rem;
    margin: 0;
}

.odoo-detail .detail-content {
    margin-bottom: 20px;
}

.odoo-detail .detail-content .row {
    margin-bottom: 10px;
}

.odoo-detail .detail-content .label {
    font-weight: 600;
    color: var(--dark-text);
}

.odoo-detail .detail-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--neutral);
}

/* Botones */
.odoo-btn {
    border-radius: 4px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.2s;
}

.odoo-btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

.odoo-btn-primary:hover {
    background-color: var(--accent);
    border-color: var(--accent);
    color: white;
}

.odoo-btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
    color: white;
}

.odoo-btn-secondary:hover {
    background-color: var(--neutral);
    border-color: var(--neutral);
    color: white;
}

.odoo-btn-danger {
    background-color: var(--alert);
    border-color: var(--alert);
    color: white;
}

.odoo-btn-danger:hover {
    background-color: #d9534f;
    border-color: #d9534f;
    color: white;
}

/* Badges */
.odoo-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.odoo-badge-success {
    background-color: var(--success-color);
    color: white;
}

.odoo-badge-warning {
    background-color: var(--warning-color);
    color: var(--dark-text);
}

.odoo-badge-danger {
    background-color: var(--danger-color);
    color: white;
}

/* Estilos para los menús desplegables */
.dropdown-menu {
    min-width: 10rem;
    padding: 0.5rem 0;
    z-index: 1000;
    position: absolute;
    right: auto;
    left: 0;
    transform: none !important;
}

/* Asegurar que los menús desplegables se muestren correctamente */
.btn-group {
    position: relative;
}

.dropdown-menu.show {
    display: block;
    position: absolute;
    inset: 0px auto auto 0px;
    margin: 0px;
    transform: translate3d(0px, 38px, 0px) !important;
}

/* Estilos para los elementos del menú desplegable */
.dropdown-item {
    padding: 0.5rem 1rem;
    clear: both;
    font-weight: 400;
    color: var(--dark-text);
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

.dropdown-item:hover, .dropdown-item:focus {
    color: var(--dark-text);
    text-decoration: none;
    background-color: var(--light-bg);
}

.dropdown-item.text-danger {
    color: var(--alert);
}

.dropdown-item.text-danger:hover, .dropdown-item.text-danger:focus {
    color: var(--alert);
    background-color: rgba(217, 83, 79, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
    .odoo-toolbar {
        flex-direction: column;
        align-items: flex-start;
    }

    .odoo-toolbar-right {
        margin-top: 10px;
    }

    .odoo-detail .detail-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .odoo-detail .detail-header .btn-group {
        margin-top: 10px;
    }

    /* Ajustes para menús desplegables en móviles */
    .dropdown-menu {
        position: absolute;
        left: auto;
        right: 0;
    }
}
