/**
 * أنماط CSS لنظام Google Drive في المعاينات
 */

/* أنماط SweetAlert المخصصة */
.swal-upload-popup {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
}

.swal-upload-popup .swal2-title {
    font-size: 1.5rem;
    color: #2c3e50;
}

.swal-upload-popup .upload-info p {
    margin-bottom: 8px;
    font-size: 14px;
    color: #495057;
}

.swal-upload-popup .upload-info strong {
    color: #2c3e50;
}

/* أنماط شريط التقدم */
.progress {
    height: 25px;
    border-radius: 15px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    border-radius: 15px;
    transition: width 0.3s ease;
    font-size: 12px;
    line-height: 25px;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

/* أنماط معاينة الملف */
.upload-preview {
    text-align: center;
    padding: 20px;
}

.upload-preview .file-info {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 20px;
    background-color: #f8f9fa;
}

.upload-preview .file-info i {
    color: #dc3545;
    margin-bottom: 10px;
}

.upload-preview .upload-info {
    background-color: #e3f2fd;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.upload-preview .upload-info h6 {
    color: #1976d2;
    margin-bottom: 10px;
}

/* أنماط معلومات النجاح */
.success-info {
    text-align: center;
    padding: 20px;
}

.success-info p {
    margin-bottom: 10px;
}

.success-info hr {
    margin: 15px 0;
    border-color: #28a745;
}

/* أنماط أيقونة Google Drive */
.google-drive-icon {
    color: #4285f4;
}

.fab.fa-google-drive {
    color: #4285f4;
}

/* أنماط الأزرار */
.btn-google-drive {
    background-color: #4285f4;
    border-color: #4285f4;
    color: white;
}

.btn-google-drive:hover {
    background-color: #3367d6;
    border-color: #3367d6;
    color: white;
}

.btn-outline-google-drive {
    color: #4285f4;
    border-color: #4285f4;
}

.btn-outline-google-drive:hover {
    background-color: #4285f4;
    border-color: #4285f4;
    color: white;
}

/* أنماط بطاقات الإحصائيات */
.stat-card {
    padding: 20px;
    border-radius: 10px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-card h4 {
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-card h6 {
    font-size: 14px;
    margin-bottom: 5px;
}

.stat-card p {
    margin-bottom: 0;
    font-size: 12px;
    color: #6c757d;
}

/* أنماط التنبيهات */
.alert-google-drive {
    background-color: #e3f2fd;
    border-color: #4285f4;
    color: #1976d2;
}

.alert-google-drive .alert-heading {
    color: #1976d2;
}

/* أنماط نموذج الإعدادات */
.google-drive-settings-form .form-label {
    font-weight: 600;
    color: #2c3e50;
}

.google-drive-settings-form .form-control:focus {
    border-color: #4285f4;
    box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
}

.google-drive-settings-form .form-check-input:checked {
    background-color: #4285f4;
    border-color: #4285f4;
}

/* أنماط زر اختبار الاتصال */
.test-connection-btn {
    position: relative;
    transition: all 0.3s ease;
}

.test-connection-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.test-connection-btn .spinner-border {
    width: 1rem;
    height: 1rem;
    margin-left: 5px;
}

/* أنماط معاينة اسم الملف */
#filename-preview {
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    display: inline-block;
    margin-top: 5px;
}

/* أنماط حالة الرفع */
.upload-status {
    padding: 10px;
    border-radius: 8px;
    margin-top: 10px;
}

.upload-status.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.upload-status.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.upload-status.pending {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* أنماط متجاوبة */
@media (max-width: 768px) {
    .swal-upload-popup {
        font-size: 14px;
    }
    
    .upload-preview {
        padding: 15px;
    }
    
    .stat-card {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .progress {
        height: 20px;
    }
    
    .progress-bar {
        line-height: 20px;
        font-size: 11px;
    }
}

/* أنماط الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* أنماط الأيقونات المتحركة */
.rotating-icon {
    animation: rotation 2s infinite linear;
}

@keyframes rotation {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(359deg);
    }
}
