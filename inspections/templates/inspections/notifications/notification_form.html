{% extends "base.html" %}
{% load widget_tweaks %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title mb-0">إرسال إشعار معاينة</h2>
                </div>
                <div class="card-body">
                    <!-- Inspection Summary -->
                    <div class="mb-4">
                        <h4>تفاصيل المعاينة</h4>
                        <table class="table table-bordered">
                            <tr>
                                <th width="30%">رقم المعاينة:</th>
                                <td>#{{ inspection.id }}</td>
                            </tr>
                            <tr>
                                <th>العميل:</th>
                                <td>{{ inspection.customer.name }}</td>
                            </tr>
                            <tr>
                                <th>المعاين:</th>
                                <td>{{ inspection.inspector.get_full_name }}</td>
                            </tr>
                            <tr>
                                <th>حالة المعاينة:</th>
                                <td>
                                    {% if inspection.status == 'pending' %}
                                        <span class="badge bg-warning">معلقة</span>
                                    {% elif inspection.status == 'completed' %}
                                        <span class="badge bg-success">مكتملة</span>
                                    {% elif inspection.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغاة</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Recipients -->
                        <div class="mb-3">
                            <label for="{{ form.recipients.id_for_label }}" class="form-label">المستلمون*</label>
                            {% render_field form.recipients class="form-select" %}
                            {% if form.recipients.errors %}
                                <div class="text-danger">
                                    {% for error in form.recipients.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">يمكنك اختيار أكثر من مستلم باستخدام Ctrl + النقر</small>
                        </div>

                        <!-- Title -->
                        <div class="mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">عنوان الإشعار*</label>
                            {% render_field form.title class="form-control" %}
                            {% if form.title.errors %}
                                <div class="text-danger">
                                    {% for error in form.title.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Message -->
                        <div class="mb-3">
                            <label for="{{ form.message.id_for_label }}" class="form-label">نص الإشعار*</label>
                            {% render_field form.message class="form-control" rows="4" %}
                            {% if form.message.errors %}
                                <div class="text-danger">
                                    {% for error in form.message.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Priority -->
                        <div class="mb-3">
                            <label for="{{ form.priority.id_for_label }}" class="form-label">الأولوية</label>
                            {% render_field form.priority class="form-select" %}
                            {% if form.priority.errors %}
                                <div class="text-danger">
                                    {% for error in form.priority.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> إرسال الإشعار
                            </button>
                            <a href="{% url 'inspections:inspection_detail' inspection.pk %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
