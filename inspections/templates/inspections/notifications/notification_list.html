{% extends "base.html" %}

{% block content %}
<div class="container">
    <h1>إشعارات المعاينات</h1>

    {% if notifications %}
        <div class="list-group">
            {% for notification in notifications %}
                <div class="list-group-item {% if not notification.is_read %}list-group-item-warning{% endif %}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1">{{ notification.title }}</h5>
                            <p class="mb-1">{{ notification.message }}</p>
                            <small class="text-muted">
                                من: {{ notification.sender.get_full_name }}
                                | {{ notification.created_at|date:"Y-m-d H:i" }}
                            </small>
                        </div>
                        <div>
                            {% if not notification.is_read %}
                                <a href="{% url 'inspections:mark_notification_read' notification.pk %}" class="btn btn-sm btn-success">
                                    <i class="fas fa-check"></i> تم القراءة
                                </a>
                            {% endif %}
                            <a href="{% url 'inspections:inspection_detail' notification.inspection.pk %}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i> عرض المعاينة
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        {% if is_paginated %}
            <nav class="mt-3">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابق</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالي</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="alert alert-info">
            لا توجد إشعارات للعرض.
        </div>
    {% endif %}

    <div class="mt-3">
        <a href="{% url 'inspections:dashboard' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> عودة للوحة التحكم
        </a>
    </div>
</div>
{% endblock %}
