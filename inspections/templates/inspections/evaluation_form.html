{% extends 'base.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{% trans 'تقييم المعاينة' %} - {{ inspection.contract_number }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">{% trans 'تقييم المعاينة' %}</h3>
                </div>
                <div class="card-body">
                    <!-- Inspection Details Summary -->
                    <div class="alert alert-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{% trans 'رقم العقد' %}:</strong> {{ inspection.contract_number }}<br>
                                <strong>{% trans 'العميل' %}:</strong> {{ inspection.customer|default:"عميل جديد" }}
                            </div>
                            <div class="col-md-6">
                                <strong>{% trans 'تاريخ المعاينة' %}:</strong> {{ inspection.scheduled_date }}<br>
                                <strong>{% trans 'الفرع' %}:</strong> {{ inspection.branch.name }}
                            </div>
                        </div>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Multiple Criteria Ratings -->
                        <div class="form-group mb-4">
                            <label class="form-label fw-bold">{% trans 'تقييم كل معيار' %}</label>
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>{% trans 'المعيار' %}</th>
                                        <th>{% trans 'التقييم' %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{{ form.location.label }}</td>
                                        <td>{% for radio in form.location %}<span class="me-2">{{ radio.tag }} {{ radio.choice_label }}</span>{% endfor %}</td>
                                    </tr>
                                    <tr>
                                        <td>{{ form.condition.label }}</td>
                                        <td>{% for radio in form.condition %}<span class="me-2">{{ radio.tag }} {{ radio.choice_label }}</span>{% endfor %}</td>
                                    </tr>
                                    <tr>
                                        <td>{{ form.suitability.label }}</td>
                                        <td>{% for radio in form.suitability %}<span class="me-2">{{ radio.tag }} {{ radio.choice_label }}</span>{% endfor %}</td>
                                    </tr>
                                    <tr>
                                        <td>{{ form.safety.label }}</td>
                                        <td>{% for radio in form.safety %}<span class="me-2">{{ radio.tag }} {{ radio.choice_label }}</span>{% endfor %}</td>
                                    </tr>
                                    <tr>
                                        <td>{{ form.accessibility.label }}</td>
                                        <td>{% for radio in form.accessibility %}<span class="me-2">{{ radio.tag }} {{ radio.choice_label }}</span>{% endfor %}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>


                        <!-- Notes Field -->
                        <div class="form-group mb-4">
                            <label for="{{ form.notes.id_for_label }}" class="form-label fw-bold">
                                {% trans 'ملاحظات التقييم' %}
                            </label>
                            {% render_field form.notes class="form-control" rows="4" %}
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                {% trans 'يمكنك إضافة أي ملاحظات أو تفاصيل إضافية عن التقييم' %}
                            </small>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'inspections:inspection_detail' inspection.pk %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> {% trans 'إلغاء' %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> {% trans 'حفظ التقييم' %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Enable any Bootstrap tooltips
        $('[data-toggle="tooltip"]').tooltip();

        // Highlight stars on hover
        $('.rating-stars .form-check-input').on('mouseenter', function() {
            var value = $(this).val();
            $(this).closest('.form-check').find('.fa-star').each(function(index) {
                if (index < value) {
                    $(this).removeClass('far').addClass('fas text-warning');
                } else {
                    $(this).removeClass('fas text-warning').addClass('far');
                }
            });
        }).on('mouseleave', function() {
            var checked = $('.rating-stars .form-check-input:checked').val();
            if (!checked) {
                $('.rating-stars .fa-star').removeClass('fas text-warning').addClass('far');
            }
        });
    });
</script>
{% endblock %}
