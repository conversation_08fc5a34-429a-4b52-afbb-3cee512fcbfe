{% extends 'base.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}{% trans 'إنشاء تنبيه جديد' %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">{% trans 'إنشاء تنبيه جديد' %}</h3>
                </div>
                <div class="card-body">
                    <!-- Inspection Details -->
                    <div class="alert alert-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{% trans 'رقم العقد' %}:</strong> {{ inspection.contract_number }}<br>
                                <strong>{% trans 'العميل' %}:</strong> {{ inspection.customer|default:"عميل جديد" }}
                            </div>
                            <div class="col-md-6">
                                <strong>{% trans 'تاريخ المعاينة' %}:</strong> {{ inspection.scheduled_date }}<br>
                                <strong>{% trans 'الحالة' %}:</strong> {{ inspection.get_status_display }}
                            </div>
                        </div>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Type Field -->
                        <div class="form-group mb-4">
                            <label for="{{ form.type.id_for_label }}" class="form-label fw-bold">
                                {% trans 'نوع التنبيه' %}
                            </label>
                            {% render_field form.type class="form-select" %}
                            {% if form.type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                {% trans 'اختر نوع التنبيه المناسب' %}
                            </small>
                        </div>

                        <!-- Message Field -->
                        <div class="form-group mb-4">
                            <label for="{{ form.message.id_for_label }}" class="form-label fw-bold">
                                {% trans 'نص التنبيه' %}
                            </label>
                            {% render_field form.message class="form-control" rows="3" %}
                            {% if form.message.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.message.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Schedule Field -->
                        <div class="form-group mb-4">
                            <label for="{{ form.scheduled_for.id_for_label }}" class="form-label fw-bold">
                                {% trans 'موعد التنبيه' %}
                            </label>
                            <div class="input-group">
                                {% render_field form.scheduled_for class="form-control" %}
                                <button type="button" class="btn btn-outline-secondary" id="clearSchedule">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            {% if form.scheduled_for.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.scheduled_for.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                {% trans 'اختياري: حدد موعداً لإرسال التنبيه' %}
                            </small>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'inspections:inspection_detail' inspection.pk %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> {% trans 'إلغاء' %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-bell"></i> {% trans 'إنشاء التنبيه' %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Handle type change
        $('#{{ form.type.id_for_label }}').on('change', function() {
            var type = $(this).val();
            var messageField = $('#{{ form.message.id_for_label }}');
            var defaultMessages = {
                'scheduled': '{% trans "تم جدولة موعد معاينة جديد" %}',
                'reminder': '{% trans "تذكير بموعد المعاينة" %}',
                'status_change': '{% trans "تم تغيير حالة المعاينة" %}',
                'evaluation': '{% trans "تم إضافة تقييم جديد للمعاينة" %}'
            };
            if (type in defaultMessages) {
                messageField.val(defaultMessages[type]);
            }
        });

        // Clear schedule button
        $('#clearSchedule').click(function() {
            $('#{{ form.scheduled_for.id_for_label }}').val('');
        });

        // Enable Bootstrap tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
{% endblock %}
