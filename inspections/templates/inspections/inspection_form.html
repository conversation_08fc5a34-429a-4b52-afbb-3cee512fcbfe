{% extends 'base.html' %}
{% load i18n %}
{% load widget_tweaks %}
{% load static %}

{% block title %}
    {% if inspection %}
        {% trans 'تعديل معاينة' %}
    {% else %}
        {% trans 'معاينة جديدة' %}
    {% endif %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'inspections/css/google_drive.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        {% if inspection %}
                            {% trans 'تعديل معاينة' %}
                        {% else %}
                            {% trans 'معاينة جديدة' %}
                        {% endif %}
                    </h3>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.customer.id_for_label }}" class="form-label">{% trans 'العميل' %}</label>
                                    {% render_field form.customer class="form-control select2" id="customer-select" %}
                                    {% if form.customer.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.customer.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.inspector.id_for_label }}" class="form-label">{% trans 'المعاين' %}</label>
                                    {% render_field form.inspector class="form-control select2" %}
                                    {% if form.inspector.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.inspector.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3" id="customer-address-container" style="display: none;">
                            <label class="form-label">{% trans 'عنوان العميل' %}</label>
                            <div class="form-control bg-light" id="customer-address" style="height: auto; min-height: 60px;"></div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.request_date.id_for_label }}" class="form-label">{% trans 'تاريخ الطلب' %}</label>
                                    {% render_field form.request_date class="form-control" %}
                                    {% if form.request_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.request_date.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.scheduled_date.id_for_label }}" class="form-label">{% trans 'تاريخ التنفيذ' %}</label>
                                    {% render_field form.scheduled_date class="form-control" %}
                                    {% if form.scheduled_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.scheduled_date.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">{% trans 'الحالة' %}</label>
                                    {% render_field form.status class="form-control" %}
                                    {% if form.status.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.status.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group" id="result-group">
                                    <label for="{{ form.result.id_for_label }}" class="form-label">{% trans 'النتيجة' %}</label>
                                    {% render_field form.result class="form-control" %}
                                    {% if form.result.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.result.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        {% if not user.is_superuser %}
                            {{ form.branch.as_hidden }}
                        {% else %}
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.branch.id_for_label }}" class="form-label">{% trans 'الفرع' %}</label>
                                    {% render_field form.branch class="form-control select2" %}
                                    {% if form.branch.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.branch.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.responsible_employee.id_for_label }}" class="form-label">{% trans 'البائع' %}</label>
                                    {% render_field form.responsible_employee class="form-control select2" %}
                                    {% if form.responsible_employee.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.responsible_employee.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.inspection_file.id_for_label }}" class="form-label">
                                        <i class="fas fa-file-pdf"></i> {% trans 'ملف المعاينة' %}
                                    </label>
                                    {% render_field form.inspection_file class="form-control" accept=".pdf" %}

                                    <!-- معاينة اسم الملف في Google Drive -->
                                    <div class="mt-2">
                                        <div class="alert alert-info py-2">
                                            <i class="fab fa-google-drive text-primary"></i>
                                            <strong>رفع تلقائي إلى Google Drive:</strong>
                                            <br>
                                            <small class="text-muted">
                                                سيتم رفع الملف تلقائياً إلى Google Drive عند الحفظ
                                                <br>
                                                <strong>اسم الملف:</strong> <span id="filename-preview" class="text-primary">سيتم توليده تلقائياً</span>
                                            </small>
                                        </div>
                                    </div>

                                    <!-- حالة الرفع إلى Google Drive -->
                                    {% if inspection.is_uploaded_to_drive %}
                                    <div class="mt-2">
                                        <div class="alert alert-success py-2">
                                            <i class="fas fa-check-circle"></i>
                                            <strong>تم الرفع إلى Google Drive بنجاح!</strong>
                                            <br>
                                            <a href="{{ inspection.google_drive_file_url }}" target="_blank" class="btn btn-sm btn-primary mt-1">
                                                <i class="fab fa-google-drive"></i> عرض في Google Drive
                                            </a>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if form.inspection_file.help_text %}
                                        <small class="text-muted">{{ form.inspection_file.help_text }}</small>
                                    {% endif %}
                                    {% if form.inspection_file.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.inspection_file.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.windows_count.id_for_label }}" class="form-label">{% trans 'عدد الشبابيك' %}</label>
                                    {% render_field form.windows_count class="form-control" type="number" min="0" %}
                                    {% if form.windows_count.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.windows_count.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Notes section -->
                        <div class="mb-4">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">{% trans 'ملاحظات المعاينة' %}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="{{ form.notes.id_for_label }}" class="form-label fw-bold">
                                            {% trans 'ملاحظات المعاينة' %}:
                                        </label>
                                        {{ form.notes|add_class:"form-control" }}
                                        <small class="text-muted mt-1 d-block">{% trans 'أدخل ملاحظات خاصة بعملية المعاينة هنا' %}</small>
                                        {% if form.notes.errors %}
                                            <div class="invalid-feedback d-block">{{ form.notes.errors }}</div>
                                        {% endif %}
                                    </div>

                                    {% if inspection.order_notes %}
                                    <div class="mt-4">
                                        <div class="alert alert-info">
                                            <h6 class="alert-heading fw-bold">{% trans 'ملاحظات من الطلب (للعلم فقط)' %}:</h6>
                                            <hr>
                                            <p class="mb-0">{{ inspection.order_notes|linebreaks }}</p>
                                        </div>
                                        <div class="text-muted small"><i class="fas fa-info-circle"></i> {% trans 'هذه الملاحظات للعلم فقط ولا يمكن تعديلها هنا. يمكن التعديل من صفحة الطلب.' %}</div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'inspections:inspection_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> {% trans 'إلغاء' %}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if inspection %}
                                    {% trans 'حفظ التغييرات' %}
                                {% else %}
                                    {% trans 'إنشاء المعاينة' %}
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="{% static 'inspections/js/google_drive_upload.js' %}"></script>
<script>
$(document).ready(function() {
    // تهيئة Select2 للقوائم المنسدلة فقط
    $('.select2').select2({
        theme: 'bootstrap4',
        language: 'ar',
        dir: 'rtl'
    });

    // التحكم في إظهار حقل النتيجة حسب الحالة
    $('#{{ form.status.id_for_label }}').on('change', function() {
        var status = $(this).val();
        var resultGroup = $('#result-group');
        var resultField = $('#{{ form.result.id_for_label }}');

        if (status === 'completed') {
            resultGroup.show();
            resultField.prop('required', true);
        } else {
            resultGroup.hide();
            resultField.prop('required', false);
            resultField.val('');
        }
    }).trigger('change');

    // عرض عنوان العميل فقط
    $('#customer-select').on('change', function() {
        var customerId = $(this).val();
        if (customerId) {
            $.ajax({
                url: '/customers/api/customer/' + customerId + '/',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.address) {
                        $('#customer-address').text(data.address);
                        $('#customer-address-container').show();
                    } else {
                        $('#customer-address').text('لا يوجد عنوان مسجل');
                        $('#customer-address-container').show();
                    }
                },
                error: function() {
                    $('#customer-address').text('حدث خطأ أثناء جلب بيانات العميل');
                    $('#customer-address-container').show();
                }
            });
        } else {
            $('#customer-address-container').hide();
        }
    });

    // تنفيذ تغيير العميل عند التحميل إذا كان هناك عميل محدد
    if ($('#customer-select').val()) {
        $('#customer-select').trigger('change');
    }

    // تعيين معرف المعاينة للـ JavaScript (في حالة التعديل)
    {% if inspection %}
        setCurrentInspectionId({{ inspection.id }});

        // تحقق من حالة الرفع كل 3 ثوان إذا كان الملف لم يتم رفعه بعد
        {% if inspection.inspection_file and not inspection.is_uploaded_to_drive %}
        checkUploadStatus({{ inspection.id }});
        {% endif %}
    {% endif %}
});

// دالة للتحقق من حالة الرفع
function checkUploadStatus(inspectionId) {
    let attempts = 0;
    const maxAttempts = 30; // 30 محاولة = دقيقة ونصف

    console.log('بدء التحقق من حالة الرفع للمعاينة:', inspectionId);

    const interval = setInterval(function() {
        attempts++;
        console.log('محاولة التحقق رقم:', attempts);

        $.ajax({
            url: '/inspections/' + inspectionId + '/check-upload-status/',
            type: 'GET',
            success: function(response) {
                console.log('استجابة التحقق:', response);

                if (response.is_uploaded && response.google_drive_url) {
                    // تم الرفع بنجاح
                    clearInterval(interval);
                    console.log('تم الرفع بنجاح! إعادة تحميل الصفحة...');

                    // عرض رسالة نجاح قبل إعادة التحميل
                    Swal.fire({
                        icon: 'success',
                        title: 'تم رفع الملف بنجاح!',
                        text: 'تم رفع ملف المعاينة إلى Google Drive',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });

                } else if (attempts >= maxAttempts) {
                    // انتهت المحاولات
                    clearInterval(interval);
                    console.log('انتهت محاولات التحقق من حالة الرفع');

                    // عرض رسالة تحذير
                    Swal.fire({
                        icon: 'warning',
                        title: 'تأخر في الرفع',
                        text: 'يرجى تحديث الصفحة للتحقق من حالة الرفع',
                        confirmButtonText: 'تحديث الصفحة',
                        showCancelButton: true,
                        cancelButtonText: 'إلغاء'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            location.reload();
                        }
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في التحقق من حالة الرفع:', error);
                if (attempts >= maxAttempts) {
                    clearInterval(interval);
                }
            }
        });
    }, 2000); // كل ثانيتين
}
</script>
{% endblock %}
