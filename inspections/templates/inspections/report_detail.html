{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ report.title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>{{ report.title }}</h2>
            <p class="text-muted">
                {{ report.get_report_type_display }} | 
                {{ report.branch.name }} | 
                {{ report.date_from|date:"Y/m/d" }} - {{ report.date_to|date:"Y/m/d" }}
            </p>
        </div>
        <div class="col-md-4 text-end">
            <button onclick="window.print()" class="btn btn-secondary">
                <i class="fas fa-print"></i> {% trans 'طباعة التقرير' %}
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6 class="card-title">{% trans 'إجمالي المعاينات' %}</h6>
                    <h2 class="mb-0">{{ report.total_inspections }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h6 class="card-title">{% trans 'المعاينات الناجحة' %}</h6>
                    <h2 class="mb-0">{{ report.successful_inspections }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <h6 class="card-title">{% trans 'المعاينات المعلقة' %}</h6>
                    <h2 class="mb-0">{{ report.pending_inspections }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h6 class="card-title">{% trans 'المعاينات الملغاة' %}</h6>
                    <h2 class="mb-0">{{ report.cancelled_inspections }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Rate Progress -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">{% trans 'نسبة النجاح' %}</h5>
            <div class="progress" style="height: 25px;">
                {% with success_rate=report.successful_inspections|default:0|divisibleby:report.total_inspections|default:1|multiply:100 %}
                <div class="progress-bar bg-success" role="progressbar" 
                     style="width: {{ success_rate }}%"
                     aria-valuenow="{{ success_rate }}" 
                     aria-valuemin="0" 
                     aria-valuemax="100">{{ success_rate }}%</div>
                {% endwith %}
            </div>
        </div>
    </div>

    <!-- Inspections List -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">{% trans 'تفاصيل المعاينات' %}</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{% trans 'رقم العقد' %}</th>
                            <th>{% trans 'العميل' %}</th>
                            <th>{% trans 'تاريخ المعاينة' %}</th>
                            <th>{% trans 'الحالة' %}</th>
                            <th>{% trans 'النتيجة' %}</th>
                            <th>{% trans 'التقييم' %}</th>
                            <th>{% trans 'الإجراءات' %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for inspection in inspections %}
                        <tr>
                            <td>{{ inspection.contract_number }}</td>
                            <td>{{ inspection.customer|default:"عميل جديد" }}</td>
                            <td>{{ inspection.scheduled_date|date:"Y/m/d" }}</td>
                            <td>
                                <span class="badge {% if inspection.status == 'pending' %}bg-warning
                                           {% elif inspection.status == 'completed' %}bg-success
                                           {% else %}bg-danger{% endif %}">
                                    {{ inspection.get_status_display }}
                                </span>
                            </td>
                            <td>
                                {% if inspection.result %}
                                <span class="badge {% if inspection.result == 'passed' %}bg-success{% else %}bg-danger{% endif %}">
                                    {{ inspection.get_result_display }}
                                </span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if inspection.evaluation %}
                                <span class="text-warning">
                                    {% for i in "12345"|make_list %}
                                        {% if forloop.counter <= inspection.evaluation.rating %}
                                        <i class="fas fa-star"></i>
                                        {% else %}
                                        <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'inspections:inspection_detail' inspection.pk %}" 
                                   class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Notes -->
    {% if report.notes %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">{% trans 'ملاحظات' %}</h5>
        </div>
        <div class="card-body">
            {{ report.notes|linebreaks }}
        </div>
    </div>
    {% endif %}

    <!-- Report Information -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">{% trans 'معلومات التقرير' %}</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>{% trans 'تم الإنشاء بواسطة' %}:</strong>
                        {% if report.created_by %}
                            {{ report.created_by.get_full_name|default:report.created_by.username }}
                        {% else %}
                            غير محدد
                        {% endif %}
                    </p>
                    <p><strong>{% trans 'تاريخ الإنشاء' %}:</strong> {{ report.created_at|date:"Y/m/d H:i" }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>{% trans 'الفرع' %}:</strong> {{ report.branch.name }}</p>
                    <p><strong>{% trans 'نوع التقرير' %}:</strong> {{ report.get_report_type_display }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .btn { display: none; }
        .card { border: none; }
        .card-header { background: none; }
    }
</style>
{% endblock %}
