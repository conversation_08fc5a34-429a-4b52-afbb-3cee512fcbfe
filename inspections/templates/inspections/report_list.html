{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans 'تقارير المعاينات' %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>{% trans 'تقارير المعاينات' %}</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{% url 'inspections:report_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {% trans 'إنشاء تقرير جديد' %}
            </a>
        </div>
    </div>

    <!-- Filter Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">{% trans 'نوع التقرير' %}</label>
                    <select name="report_type" class="form-select">
                        <option value="">{% trans 'كل التقارير' %}</option>
                        <option value="daily" {% if request.GET.report_type == 'daily' %}selected{% endif %}>
                            {% trans 'يومي' %}
                        </option>
                        <option value="weekly" {% if request.GET.report_type == 'weekly' %}selected{% endif %}>
                            {% trans 'أسبوعي' %}
                        </option>
                        <option value="monthly" {% if request.GET.report_type == 'monthly' %}selected{% endif %}>
                            {% trans 'شهري' %}
                        </option>
                        <option value="custom" {% if request.GET.report_type == 'custom' %}selected{% endif %}>
                            {% trans 'مخصص' %}
                        </option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans 'من تاريخ' %}</label>
                    <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">{% trans 'إلى تاريخ' %}</label>
                    <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to }}">
                </div>
                {% if user.is_superuser %}
                <div class="col-md-3">
                    <label class="form-label">{% trans 'الفرع' %}</label>
                    <select name="branch" class="form-select">
                        <option value="">{% trans 'كل الفروع' %}</option>
                        {% for branch in branches %}
                        <option value="{{ branch.id }}" {% if request.GET.branch|add:"0" == branch.id %}selected{% endif %}>
                            {{ branch.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}
                <div class="col-12">
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-search"></i> {% trans 'بحث' %}
                    </button>
                    <a href="{% url 'inspections:report_list' %}" class="btn btn-light">
                        <i class="fas fa-times"></i> {% trans 'إعادة تعيين' %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    {% if reports %}
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>{% trans 'عنوان التقرير' %}</th>
                    <th>{% trans 'نوع التقرير' %}</th>
                    <th>{% trans 'الفرع' %}</th>
                    <th>{% trans 'الفترة' %}</th>
                    <th>{% trans 'إجمالي المعاينات' %}</th>
                    <th>{% trans 'نسبة النجاح' %}</th>
                    <th>{% trans 'تاريخ الإنشاء' %}</th>
                    <th>{% trans 'الإجراءات' %}</th>
                </tr>
            </thead>
            <tbody>
                {% for report in reports %}
                <tr>
                    <td>{{ report.title }}</td>
                    <td>{{ report.get_report_type_display }}</td>
                    <td>{{ report.branch.name }}</td>
                    <td>
                        {{ report.date_from|date:"Y/m/d" }} - {{ report.date_to|date:"Y/m/d" }}
                    </td>
                    <td>{{ report.total_inspections }}</td>
                    <td>
                        {% if report.total_inspections > 0 %}
                            {% widthratio report.successful_inspections report.total_inspections 100 %}%
                        {% else %}
                            0%
                        {% endif %}
                    </td>
                    <td>{{ report.created_at|date:"Y/m/d" }}</td>
                    <td>
                        <a href="{% url 'inspections:report_detail' report.pk %}" 
                           class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="#" onclick="window.print()" 
                           class="btn btn-sm btn-secondary" title="{% trans 'طباعة' %}">
                            <i class="fas fa-print"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="{% trans 'تصفح الصفحات' %}">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
            <li class="page-item {% if num == page_obj.number %}active{% endif %}">
                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
            </li>
            {% endfor %}
            
            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="alert alert-info">
        {% trans 'لا توجد تقارير متاحة.' %}
    </div>
    {% endif %}
</div>
{% endblock %}
