{% extends 'base.html' %}

{% block title %}قائمة العملاء - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">قائمة العملاء</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'customers:customer_create' %}" class="btn" style="background-color: var(--primary); color: white;">
                <i class="fas fa-plus"></i> إضافة عميل جديد
            </a>
        </div>
    </div>

    <!-- Search Form -->
    <div class="card mb-4" style="border-color: var(--neutral);">
        <div class="card-body">
            <form method="get" action="{% url 'customers:customer_list' %}">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="input-group">
                            {{ form.search }}
                            <button type="submit" class="btn" style="background-color: var(--primary); color: white;">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">                    <div class="col-md-3 mb-2">
                        <label for="{{ form.category.id_for_label }}" class="form-label">التصنيف</label>
                        {{ form.category }}
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="{{ form.branch.id_for_label }}" class="form-label">الفرع</label>
                        {{ form.branch }}
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="{{ form.customer_type.id_for_label }}" class="form-label">نوع العميل</label>
                        {{ form.customer_type }}
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="{{ form.status.id_for_label }}" class="form-label">الحالة</label>
                        {{ form.status }}
                    </div>
                    <div class="col-md-3 mb-2 d-flex align-items-end">
                        <a href="{% url 'customers:customer_list' %}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Customers List -->
    <div class="card" style="border-color: var(--neutral);">
        <div class="card-header" style="background-color: var(--primary); color: white;">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-0">العملاء ({{ total_customers }})</h5>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">                    <thead>
                        <tr>                            <th>#</th>
                            <th>كود العميل</th>
                            <th>اسم العميل</th>
                            <th>نوع العميل</th>
                            <th>التصنيف</th>
                            <th>الفرع</th>
                            <th>رقم الهاتف</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in page_obj %}
                        <tr>                            <td>{{ forloop.counter }}</td>
                            <td>{{ customer.code|default:"-" }}</td>                            <td>
                                <a href="{% url 'customers:customer_detail' customer.pk %}" style="color: var(--primary); text-decoration: none; font-weight: bold;">
                                    {{ customer.name }}
                                </a>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ customer.get_customer_type_display }}</span>
                            </td>
                            <td>
                                {% if customer.category %}
                                    <span class="badge bg-secondary">{{ customer.category.name }}</span>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.branch %}
                                    {{ customer.branch.name }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>{{ customer.phone }}</td>
                            <td>{{ customer.created_at|date:"Y-m-d" }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'customers:customer_detail' customer.pk %}" class="btn btn-sm" style="background-color: var(--primary); color: white;" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'customers:customer_update' customer.pk %}" class="btn btn-sm" style="background-color: var(--light-accent); color: var(--dark-text);" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'customers:customer_delete' customer.pk %}" class="btn btn-sm" style="background-color: var(--alert); color: white;" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_value %}&category={{ category_value }}{% endif %}{% if customer_type_value %}&customer_type={{ customer_type_value }}{% endif %}{% if status_value %}&status={{ status_value }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_value %}&category={{ category_value }}{% endif %}{% if customer_type_value %}&customer_type={{ customer_type_value }}{% endif %}{% if status_value %}&status={{ status_value }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item"><a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_value %}&category={{ category_value }}{% endif %}{% if customer_type_value %}&customer_type={{ customer_type_value }}{% endif %}{% if status_value %}&status={{ status_value }}{% endif %}">{{ num }}</a></li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_value %}&category={{ category_value }}{% endif %}{% if customer_type_value %}&customer_type={{ customer_type_value }}{% endif %}{% if status_value %}&status={{ status_value }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_value %}&category={{ category_value }}{% endif %}{% if customer_type_value %}&customer_type={{ customer_type_value }}{% endif %}{% if status_value %}&status={{ status_value }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x mb-3" style="color: var(--neutral);"></i>
                <h4>لا يوجد عملاء</h4>
                <p class="text-muted">لم يتم العثور على أي عملاء. يمكنك إضافة عميل جديد من خلال الزر أعلاه.</p>
                {% if search_query %}
                <a href="{% url 'customers:customer_list' %}" class="btn btn-outline-secondary mt-3">
                    <i class="fas fa-redo"></i> إعادة تعيين البحث
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
