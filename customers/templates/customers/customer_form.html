{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 900px;
        margin: 0 auto;
    }
    
    .nav-pills .nav-link {
        color: var(--dark-text);
        border-radius: 10px;
        margin: 0 5px;
        transition: all 0.3s ease;
    }
    
    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, var(--primary), var(--secondary));
        color: white;
        box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    }
    
    .nav-pills .nav-link:hover:not(.active) {
        background-color: var(--light-accent);
        transform: translateY(-1px);
    }
    
    .tab-content {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-top: 20px;
    }
    
    .form-control, .form-select {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 14px;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.2rem rgba(52, 144, 220, 0.15);
        transform: translateY(-1px);
    }
    
    .form-label {
        font-weight: 600;
        color: var(--dark-text);
        margin-bottom: 8px;
        font-size: 14px;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .image-preview {
        width: 120px;
        height: 120px;
        border: 3px dashed #dee2e6;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }
    
    .image-preview:hover {
        border-color: var(--primary);
        background: rgba(52, 144, 220, 0.05);
    }
    
    .image-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
    }
    
    .image-preview i {
        font-size: 2rem;
        color: #6c757d;
    }
    
    .btn-save {
        background: linear-gradient(135deg, var(--primary), var(--secondary));
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 3px 15px rgba(52, 144, 220, 0.3);
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(52, 144, 220, 0.4);
        color: white;
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        padding: 12px 25px;
        border-radius: 25px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-1px);
        color: white;
    }
    
    .error-text {
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
        display: block;
    }
    
    .tab-pane {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .step-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 30px;
    }
    
    .step {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin: 0 10px;
        transition: all 0.3s ease;
    }
    
    .step.active {
        background: var(--primary);
        color: white;
        transform: scale(1.1);
    }
    
    .step.completed {
        background: var(--success);
        color: white;
    }
    
    .step-connector {
        width: 50px;
        height: 2px;
        background: #e9ecef;
        margin: 0 5px;
    }
    
    .step-connector.completed {
        background: var(--success);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}" style="color: var(--primary);">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'customers:customer_list' %}" style="color: var(--primary);">العملاء</a></li>
                    {% if customer %}
                    <li class="breadcrumb-item"><a href="{% url 'customers:customer_detail' customer.pk %}" style="color: var(--primary);">{{ customer.name }}</a></li>
                    {% endif %}
                    <li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form Container -->
    <div class="form-container">
        <!-- Header -->
        <div class="text-center mb-4">
            <h2 class="mb-2">
                {% if customer %}
                <i class="fas fa-user-edit text-primary"></i> تعديل بيانات العميل
                {% else %}
                <i class="fas fa-user-plus text-primary"></i> إضافة عميل جديد
                {% endif %}
            </h2>
            <p class="text-muted">يرجى تعبئة جميع الحقول المطلوبة بدقة</p>
        </div>

        <!-- Step Indicator -->
        <div class="step-indicator">
            <div class="step active" id="step-1">1</div>
            <div class="step-connector" id="connector-1"></div>
            <div class="step" id="step-2">2</div>
            <div class="step-connector" id="connector-2"></div>
            <div class="step" id="step-3">3</div>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-pills justify-content-center mb-0" id="customerTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="basic-tab" data-bs-toggle="pill" data-bs-target="#basic" type="button" role="tab">
                    <i class="fas fa-user me-2"></i>المعلومات الأساسية
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="contact-tab" data-bs-toggle="pill" data-bs-target="#contact" type="button" role="tab">
                    <i class="fas fa-phone me-2"></i>معلومات الاتصال
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="additional-tab" data-bs-toggle="pill" data-bs-target="#additional" type="button" role="tab">
                    <i class="fas fa-cog me-2"></i>معلومات إضافية
                </button>
            </li>
        </ul>

        <!-- Form Content -->
        <form method="post" enctype="multipart/form-data" id="customerForm">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger mt-3">
                {% for error in form.non_field_errors %}
                {{ error }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="tab-content" id="customerTabContent">
                <!-- Basic Information Tab -->
                <div class="tab-pane fade show active" id="basic" role="tabpanel">
                    <div class="row">
                        <!-- Image Upload -->
                        <div class="col-md-4 text-center">
                            <div class="form-group">
                                <label class="form-label">صورة العميل</label>
                                <div class="image-preview" id="imagePreview">
                                    {% if customer and customer.image %}
                                    <img src="{{ customer.image.url }}" alt="صورة العميل">
                                    {% else %}
                                    <i class="fas fa-camera"></i>
                                    {% endif %}
                                </div>
                                {{ form.image }}
                                {% if form.image.errors %}
                                <span class="error-text">{{ form.image.errors.0 }}</span>
                                {% endif %}
                                <small class="text-muted d-block mt-2">JPG, PNG - حد أقصى 5MB</small>
                            </div>
                        </div>

                        <!-- Basic Fields -->
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.name.id_for_label }}" class="form-label required-field">{{ form.name.label }}</label>
                                        {{ form.name }}
                                        {% if form.name.errors %}
                                        <span class="error-text">{{ form.name.errors.0 }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">                                    <div class="form-group">
                                        <label for="{{ form.customer_type.id_for_label }}" class="form-label required-field">{{ form.customer_type.label }}</label>
                                        <select name="customer_type" class="form-select" id="{{ form.customer_type.id_for_label }}">
                                            {% for choice in form.customer_type.field.choices %}
                                            <option value="{{ choice.0 }}"{% if form.customer_type.value == choice.0 %} selected{% endif %}>{{ choice.1 }}</option>
                                            {% endfor %}
                                        </select>
                                        {% if form.customer_type.errors %}
                                        <span class="error-text">{{ form.customer_type.errors.0 }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.category.id_for_label }}" class="form-label">{{ form.category.label }}</label>
                                        {{ form.category }}
                                        {% if form.category.errors %}
                                        <span class="error-text">{{ form.category.errors.0 }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                                        {{ form.status }}
                                        {% if form.status.errors %}
                                        <span class="error-text">{{ form.status.errors.0 }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-end mt-4">
                        <button type="button" class="btn btn-primary" onclick="nextTab('contact-tab')">
                            التالي <i class="fas fa-arrow-left ms-2"></i>
                        </button>
                    </div>
                </div>

                <!-- Contact Information Tab -->
                <div class="tab-pane fade" id="contact" role="tabpanel">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.phone.id_for_label }}" class="form-label required-field">{{ form.phone.label }}</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                <span class="error-text">{{ form.phone.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.phone2.id_for_label }}" class="form-label">{{ form.phone2.label }}</label>
                                {{ form.phone2 }}
                                {% if form.phone2.errors %}
                                <span class="error-text">{{ form.phone2.errors.0 }}</span>
                                {% endif %}
                                <small class="text-muted">اختياري</small>
                            </div>
                        </div>
                        {% if user.branch.is_main_branch %}
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.branch.id_for_label }}" class="form-label required-field">{{ form.branch.label }}</label>
                                {{ form.branch }}
                                {% if form.branch.errors %}
                                <span class="error-text">{{ form.branch.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <span class="error-text">{{ form.email.errors.0 }}</span>
                                {% endif %}
                                <small class="text-muted">اختياري</small>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.address.id_for_label }}" class="form-label required-field">{{ form.address.label }}</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                <span class="error-text">{{ form.address.errors.0 }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-secondary" onclick="prevTab('basic-tab')">
                            <i class="fas fa-arrow-right me-2"></i> السابق
                        </button>
                        <button type="button" class="btn btn-primary" onclick="nextTab('additional-tab')">
                            التالي <i class="fas fa-arrow-left ms-2"></i>
                        </button>
                    </div>
                </div>

                <!-- Additional Information Tab -->
                <div class="tab-pane fade" id="additional" role="tabpanel">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.interests.id_for_label }}" class="form-label">{{ form.interests.label }}</label>
                                {{ form.interests }}
                                {% if form.interests.errors %}
                                <span class="error-text">{{ form.interests.errors.0 }}</span>
                                {% endif %}
                                <small class="text-muted">اكتب اهتمامات العميل وتفضيلاته</small>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                <span class="error-text">{{ form.notes.errors.0 }}</span>
                                {% endif %}
                                <small class="text-muted">ملاحظات إضافية عن العميل</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-secondary" onclick="prevTab('contact-tab')">
                            <i class="fas fa-arrow-right me-2"></i> السابق
                        </button>
                        <div>
                            <a href="{% url 'customers:customer_list' %}" class="btn btn-cancel me-3">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-save">
                                <i class="fas fa-save me-2"></i>
                                {% if customer %}تحديث{% else %}حفظ{% endif %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image preview functionality
    const imageInput = document.getElementById('{{ form.image.id_for_label }}');
    const imagePreview = document.getElementById('imagePreview');
    
    if (imageInput) {
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreview.innerHTML = `<img src="${e.target.result}" alt="صورة العميل">`;
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    // Tab navigation
    const tabButtons = document.querySelectorAll('[data-bs-toggle="pill"]');
    tabButtons.forEach(function(button) {
        button.addEventListener('shown.bs.tab', function(e) {
            updateStepIndicator(e.target.id);
        });
    });
    
    // Form validation
    const form = document.getElementById('customerForm');
    form.addEventListener('submit', function(e) {
        if (!validateCurrentTab()) {
            e.preventDefault();
            showFirstError();
        }
    });
});

function nextTab(tabId) {
    if (validateCurrentTab()) {
        const tab = new bootstrap.Tab(document.getElementById(tabId));
        tab.show();
    } else {
        showFirstError();
    }
}

function prevTab(tabId) {
    const tab = new bootstrap.Tab(document.getElementById(tabId));
    tab.show();
}

function validateCurrentTab() {
    const activeTab = document.querySelector('.tab-pane.active');
    const requiredFields = activeTab.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

function showFirstError() {
    const firstError = document.querySelector('.is-invalid');
    if (firstError) {
        firstError.focus();
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

function updateStepIndicator(activeTabId) {
    const steps = document.querySelectorAll('.step');
    const connectors = document.querySelectorAll('.step-connector');
    
    // Reset all steps
    steps.forEach(step => {
        step.classList.remove('active', 'completed');
    });
    connectors.forEach(connector => {
        connector.classList.remove('completed');
    });
    
    // Set active and completed steps
    let stepNumber = 1;
    if (activeTabId === 'contact-tab') stepNumber = 2;
    else if (activeTabId === 'additional-tab') stepNumber = 3;
    
    for (let i = 1; i <= 3; i++) {
        const step = document.getElementById(`step-${i}`);
        const connector = document.getElementById(`connector-${i}`);
        
        if (i < stepNumber) {
            step.classList.add('completed');
            if (connector) connector.classList.add('completed');
        } else if (i === stepNumber) {
            step.classList.add('active');
        }
    }
}

// Auto-resize textareas
document.querySelectorAll('textarea').forEach(function(textarea) {
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
{% endblock %}
