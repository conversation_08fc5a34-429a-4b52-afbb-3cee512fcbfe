{% extends 'base.html' %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">{{ title }}</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">إدارة المصنع</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_line_list' %}">خطوط الإنتاج</a></li>
                    {% if production_line %}
                        <li class="breadcrumb-item"><a href="{% url 'factory:production_line_detail' production_line.pk %}">{{ production_line.name }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">تعديل</li>
                    {% else %}
                        <li class="breadcrumb-item active" aria-current="page">إضافة جديد</li>
                    {% endif %}
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'factory:production_line_list' %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                <i class="fas fa-arrow-right"></i> العودة إلى القائمة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--primary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-edit"></i> {{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-check mb-3">
                                    {{ form.is_active }}
                                    <label for="{{ form.is_active.id_for_label }}" class="form-check-label">{{ form.is_active.label }}</label>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.is_active.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.description.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <button type="submit" class="btn" style="background-color: var(--primary); color: white;">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                                <a href="{% if production_line %}{% url 'factory:production_line_detail' production_line.pk %}{% else %}{% url 'factory:production_line_list' %}{% endif %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
