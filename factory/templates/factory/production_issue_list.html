{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">المصنع</a></li>
            <li class="breadcrumb-item active" aria-current="page">مشاكل الإنتاج</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-exclamation-triangle"></i> مشاكل الإنتاج</h2>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">البحث والتصفية</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">بحث</label>
                    <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="ابحث عن عنوان المشكلة أو رقم الطلب...">
                </div>
                <div class="col-md-3">
                    <label for="severity" class="form-label">الخطورة</label>
                    <select class="form-select" id="severity" name="severity">
                        <option value="">الكل</option>
                        {% for severity_code, severity_name in severity_choices %}
                            <option value="{{ severity_code }}" {% if severity_filter == severity_code %}selected{% endif %}>{{ severity_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="resolved" class="form-label">الحالة</label>
                    <select class="form-select" id="resolved" name="resolved">
                        <option value="">الكل</option>
                        <option value="unresolved" {% if resolved_filter == 'unresolved' %}selected{% endif %}>غير محلولة</option>
                        <option value="resolved" {% if resolved_filter == 'resolved' %}selected{% endif %}>تم حلها</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Issues List -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة المشاكل ({{ total_issues }})</h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>أمر الإنتاج</th>
                                <th>الخطورة</th>
                                <th>تاريخ الإبلاغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for issue in page_obj %}
                                <tr>
                                    <td>{{ issue.title }}</td>
                                    <td>
                                        <a href="{% url 'factory:production_order_detail' issue.production_order.id %}">
                                            {{ issue.production_order.order.order_number }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if issue.severity == 'critical' %}
                                            <span class="badge bg-danger">حرجة</span>
                                        {% elif issue.severity == 'high' %}
                                            <span class="badge bg-warning text-dark">عالية</span>
                                        {% elif issue.severity == 'medium' %}
                                            <span class="badge bg-info text-dark">متوسطة</span>
                                        {% else %}
                                            <span class="badge bg-secondary">منخفضة</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ issue.reported_at|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        {% if issue.resolved %}
                                            <span class="badge bg-success">تم الحل</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير محلولة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'factory:production_issue_detail' issue.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'factory:production_issue_update' issue.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if severity_filter %}&severity={{ severity_filter }}{% endif %}{% if resolved_filter %}&resolved={{ resolved_filter }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for i in page_obj.paginator.page_range %}
                            {% if page_obj.number == i %}
                                <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                            {% else %}
                                <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if severity_filter %}&severity={{ severity_filter }}{% endif %}{% if resolved_filter %}&resolved={{ resolved_filter }}{% endif %}">{{ i }}</a></li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if severity_filter %}&severity={{ severity_filter }}{% endif %}{% if resolved_filter %}&resolved={{ resolved_filter }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> لا توجد مشاكل إنتاج مطابقة لمعايير البحث.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
