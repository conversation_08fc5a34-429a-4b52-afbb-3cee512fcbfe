{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">المصنع</a></li>
            <li class="breadcrumb-item"><a href="{% url 'factory:production_order_detail' production_order.id %}">أمر الإنتاج: {{ production_order.order.order_number }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
        </ol>
    </nav>

    <div class="card">
        <div class="card-header">
            <h4 class="mb-0">{{ title }}</h4>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">{{ form.title.label }}</label>
                            {{ form.title }}
                            {% if form.title.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.title.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="{{ form.severity.id_for_label }}" class="form-label">{{ form.severity.label }}</label>
                            {{ form.severity }}
                            {% if form.severity.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.severity.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.description.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                {% if issue %}
                <div class="form-group mb-3">
                    <div class="form-check">
                        {{ form.resolved }}
                        <label class="form-check-label" for="{{ form.resolved.id_for_label }}">
                            {{ form.resolved.label }}
                        </label>
                    </div>
                    {% if form.resolved.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.resolved.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-group mb-3">
                    <label for="{{ form.resolution_notes.id_for_label }}" class="form-label">{{ form.resolution_notes.label }}</label>
                    {{ form.resolution_notes }}
                    {% if form.resolution_notes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.resolution_notes.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                {% endif %}
                
                {{ form.production_order }}
                
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <a href="{% url 'factory:production_order_detail' production_order.id %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
