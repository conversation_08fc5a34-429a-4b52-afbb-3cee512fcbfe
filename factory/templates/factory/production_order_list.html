{% extends 'base.html' %}

{% block title %}أوامر الإنتاج - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">أوامر الإنتاج</h2>
            <p>إجمالي أوامر الإنتاج: {{ total_production_orders }}</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'factory:production_order_create' %}" class="btn" style="background-color: var(--primary); color: white;">
                <i class="fas fa-plus"></i> إضافة أمر إنتاج جديد
            </a>
        </div>
    </div>

    <!-- Search and Filter Form -->
    <div class="card mb-4" style="border-color: var(--neutral);">
        <div class="card-body">
            <form method="get" action="{% url 'factory:production_order_list' %}">
                <div class="row">
                    <div class="col-md-5 mb-2">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="البحث عن أمر إنتاج..." value="{{ search_query }}">
                            <button type="submit" class="btn" style="background-color: var(--primary); color: white;">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 mb-2">
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            {% for status_code, status_name in status_choices %}
                                <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>{{ status_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-2 text-end">
                        {% if search_query or status_filter %}
                            <a href="{% url 'factory:production_order_list' %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                                <i class="fas fa-times"></i> إلغاء التصفية
                            </a>
                        {% endif %}
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Production Orders List -->
    <div class="card" style="border-color: var(--neutral);">
        <div class="card-header" style="background-color: var(--primary); color: white;">
            <h5 class="mb-0"><i class="fas fa-tasks"></i> قائمة أوامر الإنتاج</h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>خط الإنتاج</th>
                                <th>تاريخ البدء</th>
                                <th>تاريخ الانتهاء المتوقع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in page_obj %}
                                <tr>
                                    <td>{{ order.order.order_number }}</td>
                                    <td>{{ order.production_line.name }}</td>
                                    <td>{{ order.start_date|date:"Y-m-d"|default:"-" }}</td>
                                    <td>{{ order.estimated_completion|date:"Y-m-d"|default:"-" }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if order.status == 'completed' %}bg-success
                                            {% elif order.status == 'in_progress' %}" style="background-color: var(--primary); color: white;
                                            {% elif order.status == 'quality_check' %}" style="background-color: var(--light-accent); color: var(--dark-text);
                                            {% elif order.status == 'cancelled' %}" style="background-color: var(--alert); color: white;
                                            {% else %}bg-secondary{% endif %}">
                                            {{ order.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'factory:production_order_detail' order.pk %}" class="btn btn-sm" style="background-color: var(--primary); color: white;" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'factory:production_order_update' order.pk %}" class="btn btn-sm" style="background-color: var(--light-accent); color: var(--dark-text);" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'factory:production_order_delete' order.pk %}" class="btn btn-sm" style="background-color: var(--alert); color: white;" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for i in page_obj.paginator.page_range %}
                                {% if page_obj.number == i %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ i }}</span>
                                    </li>
                                {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ i }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <p class="mb-3">لا توجد أوامر إنتاج متاحة{% if search_query or status_filter %} تطابق معايير البحث{% endif %}.</p>
                    {% if search_query or status_filter %}
                        <a href="{% url 'factory:production_order_list' %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                            <i class="fas fa-times"></i> إلغاء التصفية
                        </a>
                    {% else %}
                        <a href="{% url 'factory:production_order_create' %}" class="btn" style="background-color: var(--primary); color: white;">
                            <i class="fas fa-plus"></i> إضافة أمر إنتاج جديد
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
