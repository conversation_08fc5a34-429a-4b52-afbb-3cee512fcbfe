{% extends 'base.html' %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">{{ title }}</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:factory_list' %}">إدارة المصنع</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_order_list' %}">أوامر الإنتاج</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'factory:production_order_detail' production_order.pk %}">{{ production_order.order.order_number }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إضافة فحص جودة جديد</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'factory:production_order_detail' production_order.pk %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                <i class="fas fa-arrow-right"></i> العودة إلى أمر الإنتاج
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--alert); color: white;">
                    <h5 class="mb-0"><i class="fas fa-clipboard-check"></i> {{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        {{ form.production_order }}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.result.id_for_label }}" class="form-label">{{ form.result.label }}</label>
                                    {{ form.result }}
                                    {% if form.result.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.result.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">اختر نتيجة فحص الجودة</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                    {{ form.notes }}
                                    {% if form.notes.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.notes.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">أضف ملاحظات حول فحص الجودة</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <button type="submit" class="btn" style="background-color: var(--alert); color: white;">
                                    <i class="fas fa-save"></i> حفظ
                                </button>
                                <a href="{% url 'factory:production_order_detail' production_order.pk %}" class="btn" style="background-color: var(--light-accent); color: var(--dark-text);">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quality Check Information -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card" style="border-color: var(--neutral);">
                <div class="card-header" style="background-color: var(--secondary); color: white;">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات أمر الإنتاج</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%;">رقم الطلب</th>
                                    <td>{{ production_order.order.order_number }}</td>
                                </tr>
                                <tr>
                                    <th>خط الإنتاج</th>
                                    <td>{{ production_order.production_line.name }}</td>
                                </tr>
                                <tr>
                                    <th>الحالة</th>
                                    <td>
                                        <span class="badge 
                                            {% if production_order.status == 'completed' %}bg-success
                                            {% elif production_order.status == 'in_progress' %}" style="background-color: var(--primary); color: white;
                                            {% elif production_order.status == 'quality_check' %}" style="background-color: var(--light-accent); color: var(--dark-text);
                                            {% elif production_order.status == 'cancelled' %}" style="background-color: var(--alert); color: white;
                                            {% else %}bg-secondary{% endif %}">
                                            {{ production_order.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb"></i> ملاحظة</h6>
                                <p>سيتم تسجيل فحص الجودة هذا باسمك وتاريخ اليوم.</p>
                                <p>إذا كانت نتيجة الفحص "راسب" أو "يحتاج إعادة تصنيع"، سيتم تغيير حالة أمر الإنتاج إلى "فحص الجودة" تلقائياً.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
