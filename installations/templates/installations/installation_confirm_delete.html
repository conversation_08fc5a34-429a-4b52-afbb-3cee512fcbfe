{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "حذف التركيب" %} #{{ installation.id }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">{% trans "الرئيسية" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'installations:dashboard' %}">{% trans "التركيبات" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'installations:installation_detail' installation.id %}">{% trans "تفاصيل التركيب" %} #{{ installation.id }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{% trans "حذف التركيب" %}</li>
        </ol>
    </nav>

    <div class="card border-danger">
        <div class="card-header bg-danger text-white">
            <h4 class="mb-0">{% trans "تأكيد الحذف" %}</h4>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> 
                {% trans "هل أنت متأكد من رغبتك في حذف هذا التركيب؟ هذا الإجراء لا يمكن التراجع عنه." %}
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">{% trans "معلومات التركيب" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>{% trans "رقم التركيب:" %}</strong> {{ installation.id }}</p>
                            <p><strong>{% trans "العميل:" %}</strong> {{ installation.customer.name }}</p>
                            <p><strong>{% trans "رقم الفاتورة:" %}</strong> {{ installation.invoice_number }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "الحالة:" %}</strong> {{ installation.get_status_display }}</p>
                            <p><strong>{% trans "موعد التركيب:" %}</strong> 
                                {% if installation.scheduled_date %}
                                    {{ installation.scheduled_date|date:"Y-m-d H:i" }}
                                {% else %}
                                    <span class="text-muted">{% trans "غير محدد" %}</span>
                                {% endif %}
                            </p>
                            <p><strong>{% trans "الفرع:" %}</strong> {{ installation.branch.name }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <form method="post">
                {% csrf_token %}
                <div class="d-flex justify-content-between">
                    <a href="{% url 'installations:installation_detail' installation.id %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> {% trans "إلغاء" %}
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> {% trans "تأكيد الحذف" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
