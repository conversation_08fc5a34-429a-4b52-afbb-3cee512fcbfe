{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "تفاصيل طلب النقل" %} #{{ transport.id }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">{% trans "الرئيسية" %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'installations:transport_list' %}">{% trans "طلبات النقل" %}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{% trans "تفاصيل طلب النقل" %} #{{ transport.id }}</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{% trans "تفاصيل طلب النقل" %} #{{ transport.id }}</h1>
        <div class="btn-group" role="group">
            <a href="{% url 'installations:transport_update' transport.id %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> {% trans "تعديل" %}
            </a>
            <a href="{% url 'installations:transport_delete' transport.id %}" class="btn btn-danger">
                <i class="fas fa-trash"></i> {% trans "حذف" %}
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Transport Details -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% trans "معلومات طلب النقل" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{% trans "رقم التركيب:" %}</strong></p>
                            <p>
                                <a href="{% url 'installations:installation_detail' transport.installation.id %}" class="text-decoration-none">
                                    {{ transport.installation.id }}
                                </a>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{% trans "العميل:" %}</strong></p>
                            <p>{{ transport.installation.customer.name }}</p>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{% trans "من موقع:" %}</strong></p>
                            <p>{{ transport.from_location }}</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{% trans "إلى موقع:" %}</strong></p>
                            <p>{{ transport.to_location }}</p>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{% trans "الحالة:" %}</strong></p>
                            <p>
                                {% if transport.status == 'pending' %}
                                    <span class="badge bg-warning">{{ transport.get_status_display }}</span>
                                {% elif transport.status == 'scheduled' %}
                                    <span class="badge bg-info">{{ transport.get_status_display }}</span>
                                {% elif transport.status == 'in_progress' %}
                                    <span class="badge bg-primary">{{ transport.get_status_display }}</span>
                                {% elif transport.status == 'completed' %}
                                    <span class="badge bg-success">{{ transport.get_status_display }}</span>
                                {% elif transport.status == 'cancelled' %}
                                    <span class="badge bg-danger">{{ transport.get_status_display }}</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{% trans "موعد النقل:" %}</strong></p>
                            <p>
                                {% if transport.scheduled_date %}
                                    {{ transport.scheduled_date|date:"Y-m-d H:i" }}
                                {% else %}
                                    <span class="text-muted">{% trans "غير محدد" %}</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{% trans "السائق المسؤول:" %}</strong></p>
                            <p>
                                {% if transport.driver %}
                                    {{ transport.driver.get_full_name }}
                                {% else %}
                                    <span class="text-muted">{% trans "غير محدد" %}</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{% trans "الفرع:" %}</strong></p>
                            <p>{{ transport.installation.branch.name }}</p>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-12">
                            <p class="mb-1"><strong>{% trans "ملاحظات:" %}</strong></p>
                            <p>
                                {% if transport.notes %}
                                    {{ transport.notes|linebreaks }}
                                {% else %}
                                    <span class="text-muted">{% trans "لا توجد ملاحظات" %}</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>{% trans "تم الإنشاء بواسطة:" %}</strong> 
                                {{ transport.created_by.get_full_name }}
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>{% trans "تاريخ الإنشاء:" %}</strong> 
                                {{ transport.created_at|date:"Y-m-d H:i" }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Installation Information -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">{% trans "معلومات التركيب" %}</h5>
                </div>
                <div class="card-body">
                    <p><strong>{% trans "رقم التركيب:" %}</strong> {{ transport.installation.id }}</p>
                    <p><strong>{% trans "العميل:" %}</strong> {{ transport.installation.customer.name }}</p>
                    <p><strong>{% trans "رقم الفاتورة:" %}</strong> {{ transport.installation.invoice_number }}</p>
                    <p><strong>{% trans "حالة التركيب:" %}</strong> 
                        {% if transport.installation.status == 'pending' %}
                            <span class="badge bg-warning">{{ transport.installation.get_status_display }}</span>
                        {% elif transport.installation.status == 'scheduled' %}
                            <span class="badge bg-info">{{ transport.installation.get_status_display }}</span>
                        {% elif transport.installation.status == 'in_progress' %}
                            <span class="badge bg-primary">{{ transport.installation.get_status_display }}</span>
                        {% elif transport.installation.status == 'completed' %}
                            <span class="badge bg-success">{{ transport.installation.get_status_display }}</span>
                        {% elif transport.installation.status == 'cancelled' %}
                            <span class="badge bg-danger">{{ transport.installation.get_status_display }}</span>
                        {% endif %}
                    </p>
                    <p><strong>{% trans "موعد التركيب:" %}</strong> 
                        {% if transport.installation.scheduled_date %}
                            {{ transport.installation.scheduled_date|date:"Y-m-d H:i" }}
                        {% else %}
                            <span class="text-muted">{% trans "غير محدد" %}</span>
                        {% endif %}
                    </p>
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'installations:installation_detail' transport.installation.id %}" class="btn btn-sm btn-outline-info w-100">
                        <i class="fas fa-tools"></i> {% trans "عرض تفاصيل التركيب" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Update -->
    <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">{% trans "تحديث الحالة" %}</h5>
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'installations:transport_update' transport.id %}" class="row g-3">
                {% csrf_token %}
                <div class="col-md-4">
                    <label for="status" class="form-label">{% trans "الحالة الجديدة" %}</label>
                    <select name="status" id="status" class="form-select">
                        {% for status_value, status_name in transport.STATUS_CHOICES %}
                            <option value="{{ status_value }}" {% if transport.status == status_value %}selected{% endif %}>
                                {{ status_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="driver" class="form-label">{% trans "السائق المسؤول" %}</label>
                    <select name="driver" id="driver" class="form-select">
                        <option value="">{% trans "- اختر السائق -" %}</option>
                        {% for driver in drivers %}
                            <option value="{{ driver.id }}" {% if transport.driver == driver %}selected{% endif %}>
                                {{ driver.get_full_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-save"></i> {% trans "تحديث الحالة" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
