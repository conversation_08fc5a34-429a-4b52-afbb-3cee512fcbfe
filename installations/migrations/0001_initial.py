# Generated by Django 4.2.21 on 2025-07-02 13:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounts', '0001_initial'),
        ('inspections', '0001_initial'),
        ('orders', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Installation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التركيب المجدول')),
                ('actual_start_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ بدء التركيب الفعلي')),
                ('actual_end_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء التركيب الفعلي')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('scheduled', 'مجدول'), ('in_progress', 'جاري التنفيذ'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('quality_rating', models.IntegerField(blank=True, choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], null=True, verbose_name='تقييم الجودة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_installations', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('inspection', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='installations', to='inspections.inspection', verbose_name='المعاينة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='installations', to='orders.order', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'عملية تركيب',
                'verbose_name_plural': 'عمليات التركيب',
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='InstallationTeam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفريق')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='installation_teams', to='accounts.branch', verbose_name='الفرع')),
                ('leader', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='led_teams', to=settings.AUTH_USER_MODEL, verbose_name='قائد الفريق')),
                ('members', models.ManyToManyField(related_name='installation_teams', to=settings.AUTH_USER_MODEL, verbose_name='أعضاء الفريق')),
            ],
            options={
                'verbose_name': 'فريق تركيب',
                'verbose_name_plural': 'فرق التركيب',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='InstallationNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('scheduled', 'موعد تركيب'), ('status_change', 'تغيير الحالة'), ('quality_check', 'فحص الجودة'), ('issue', 'مشكلة')], max_length=20, verbose_name='نوع الإشعار')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الإشعار')),
                ('message', models.TextField(verbose_name='نص الإشعار')),
                ('is_read', models.BooleanField(default=False, verbose_name='تمت القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('installation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='installations.installation', verbose_name='عملية التركيب')),
            ],
            options={
                'verbose_name': 'إشعار تركيب',
                'verbose_name_plural': 'إشعارات التركيب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InstallationIssue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المشكلة')),
                ('description', models.TextField(verbose_name='وصف المشكلة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('critical', 'حرجة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('open', 'مفتوحة'), ('in_progress', 'جاري المعالجة'), ('resolved', 'تم الحل'), ('closed', 'مغلقة')], default='open', max_length=20, verbose_name='الحالة')),
                ('resolution', models.TextField(blank=True, verbose_name='الحل')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_installation_issues', to=settings.AUTH_USER_MODEL, verbose_name='تم التكليف إلى')),
                ('installation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='issues', to='installations.installation', verbose_name='عملية التركيب')),
                ('reported_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reported_installation_issues', to=settings.AUTH_USER_MODEL, verbose_name='تم الإبلاغ بواسطة')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_installation_issues', to=settings.AUTH_USER_MODEL, verbose_name='تم الحل بواسطة')),
            ],
            options={
                'verbose_name': 'مشكلة تركيب',
                'verbose_name_plural': 'مشاكل التركيب',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='installation',
            name='team',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='installations', to='installations.installationteam', verbose_name='فريق التركيب'),
        ),
        migrations.CreateModel(
            name='InstallationStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الخطوة')),
                ('description', models.TextField(blank=True, verbose_name='وصف الخطوة')),
                ('order', models.PositiveIntegerField(verbose_name='الترتيب')),
                ('is_completed', models.BooleanField(default=False, verbose_name='مكتملة')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='installations/steps/', verbose_name='صورة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('completed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_steps', to=settings.AUTH_USER_MODEL, verbose_name='تم الإكمال بواسطة')),
                ('installation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='installations.installation', verbose_name='عملية التركيب')),
            ],
            options={
                'verbose_name': 'خطوة تركيب',
                'verbose_name_plural': 'خطوات التركيب',
                'ordering': ['installation', 'order'],
                'unique_together': {('installation', 'order')},
            },
        ),
        migrations.CreateModel(
            name='InstallationQualityCheck',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('criteria', models.CharField(choices=[('alignment', 'المحاذاة'), ('finishing', 'التشطيب'), ('functionality', 'الوظائف'), ('safety', 'السلامة'), ('cleanliness', 'النظافة')], max_length=20, verbose_name='معيار التقييم')),
                ('rating', models.IntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جداً'), (5, 'ممتاز')], verbose_name='التقييم')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='installations/quality/', verbose_name='صورة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الفحص')),
                ('checked_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='quality_checks', to=settings.AUTH_USER_MODEL, verbose_name='تم الفحص بواسطة')),
                ('installation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quality_checks', to='installations.installation', verbose_name='عملية التركيب')),
            ],
            options={
                'verbose_name': 'فحص جودة',
                'verbose_name_plural': 'فحوصات الجودة',
                'ordering': ['-created_at'],
                'unique_together': {('installation', 'criteria')},
            },
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['order'], name='install_order_idx'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['inspection'], name='install_inspection_idx'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['team'], name='install_team_idx'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['status'], name='install_status_idx'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['scheduled_date'], name='install_sched_date_idx'),
        ),
        migrations.AddIndex(
            model_name='installation',
            index=models.Index(fields=['created_at'], name='install_created_idx'),
        ),
    ]
