# Generated by Django 4.2.21 on 2025-07-02 13:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التقرير')),
                ('report_type', models.CharField(choices=[('sales', 'تقرير المبيعات'), ('production', 'تقرير الإنتاج'), ('inventory', 'تقرير المخزون'), ('financial', 'تقرير مالي'), ('analytics', 'تقرير تحليلي'), ('custom', 'تقرير مخصص')], max_length=20, verbose_name='نوع التقرير')),
                ('description', models.TextField(blank=True, verbose_name='وصف التقرير')),
                ('parameters', models.JSONField(blank=True, default=dict, verbose_name='معلمات التقرير')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reports_created', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'تقرير',
                'verbose_name_plural': 'التقارير',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SavedReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم النتيجة المحفوظة')),
                ('data', models.JSONField(default=dict, verbose_name='بيانات التقرير')),
                ('parameters_used', models.JSONField(blank=True, default=dict, verbose_name='المعلمات المستخدمة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='saved_reports', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='saved_results', to='reports.report', verbose_name='التقرير')),
            ],
            options={
                'verbose_name': 'تقرير محفوظ',
                'verbose_name_plural': 'التقارير المحفوظة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الجدولة')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي')], max_length=20, verbose_name='التكرار')),
                ('parameters', models.JSONField(blank=True, default=dict, verbose_name='معلمات التقرير')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='report_schedules_created', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('recipients', models.ManyToManyField(related_name='report_subscriptions', to=settings.AUTH_USER_MODEL, verbose_name='المستلمون')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='reports.report', verbose_name='التقرير')),
            ],
            options={
                'verbose_name': 'جدولة تقرير',
                'verbose_name_plural': 'جدولات التقارير',
                'ordering': ['-created_at'],
            },
        ),
    ]
