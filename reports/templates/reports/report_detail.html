{% extends 'base.html' %}
{% load i18n %}

{% block title %}{{ report.title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>{{ report.title }}</h2>
        <div class="btn-group">
            <a href="{% url 'reports:report_update' report.pk %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> {% trans 'تعديل التقرير' %}
            </a>
            <a href="{% url 'reports:schedule_create' report.pk %}" class="btn btn-success">
                <i class="fas fa-clock"></i> {% trans 'جدولة التقرير' %}
            </a>
        </div>
    </div>

    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="row">
        <!-- Report Information -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans 'معلومات التقرير' %}</h5>
                </div>
                <div class="card-body">
                    <dl>
                        <dt>{% trans 'نوع التقرير' %}</dt>
                        <dd>{{ report.get_report_type_display }}</dd>

                        <dt>{% trans 'الوصف' %}</dt>
                        <dd>{{ report.description|default:_('لا يوجد وصف') }}</dd>

                        <dt>{% trans 'تم الإنشاء بواسطة' %}</dt>
                        <dd>{{ report.created_by.get_full_name|default:report.created_by.username }}</dd>

                        <dt>{% trans 'تاريخ الإنشاء' %}</dt>
                        <dd>{{ report.created_at|date:"Y-m-d H:i" }}</dd>

                        <dt>{% trans 'آخر تحديث' %}</dt>
                        <dd>{{ report.updated_at|date:"Y-m-d H:i" }}</dd>

                        <dt>{% trans 'المعلمات' %}</dt>
                        <dd>
                            <pre class="bg-light p-2 rounded"><code>{{ report.parameters|default:"{}" }}</code></pre>
                        </dd>
                    </dl>
                </div>
            </div>

            <!-- Report Schedules -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans 'جدولات التقرير' %}</h5>
                </div>
                <div class="card-body">
                    {% if report.schedules.exists %}
                        <ul class="list-group">
                        {% for schedule in report.schedules.all %}
                            <li class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ schedule.name }}</h6>
                                        <small class="text-muted">{{ schedule.get_frequency_display }}</small>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'reports:schedule_update' schedule.pk %}" class="btn btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'reports:schedule_delete' schedule.pk %}" class="btn btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </li>
                        {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted">{% trans 'لا توجد جدولات للتقرير' %}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Report Results -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{% trans 'نتائج التقرير' %}</h5>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#saveReportModal">
                        <i class="fas fa-save"></i> {% trans 'حفظ النتائج' %}
                    </button>
                </div>
                <div class="card-body">
                    {% if report_data %}
                        {% if report.report_type == 'sales' %}
                            {% include 'reports/includes/sales_report.html' with data=report_data %}
                        {% elif report.report_type == 'production' %}
                            {% include 'reports/includes/production_report.html' with data=report_data %}
                        {% elif report.report_type == 'inventory' %}
                            {% include 'reports/includes/inventory_report.html' with data=report_data %}
                        {% elif report.report_type == 'financial' %}
                            {% include 'reports/includes/financial_report.html' with data=report_data %}
                        {% elif report.report_type == 'analytics' %}
                            {% include 'reports/includes/analytics_report.html' with data=report_data %}
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info">
                            {% trans 'لا توجد بيانات متاحة للتقرير' %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Saved Results -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans 'النتائج المحفوظة' %}</h5>
                </div>
                <div class="card-body">
                    {% if saved_results %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{% trans 'الاسم' %}</th>
                                        <th>{% trans 'تاريخ الحفظ' %}</th>
                                        <th>{% trans 'تم الحفظ بواسطة' %}</th>
                                        <th>{% trans 'الإجراءات' %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for result in saved_results %}
                                    <tr>
                                        <td>{{ result.name }}</td>
                                        <td>{{ result.created_at|date:"Y-m-d H:i" }}</td>
                                        <td>{{ result.created_by.get_full_name|default:result.created_by.username }}</td>
                                        <td>
                                            <button type="button" class="btn btn-info btn-sm view-saved-result" 
                                                    data-result='{{ result.data|safe }}'
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#viewSavedResultModal">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">{% trans 'لا توجد نتائج محفوظة' %}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Save Report Modal -->
<div class="modal fade" id="saveReportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'reports:save_report_result' report.pk %}">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title">{% trans 'حفظ نتائج التقرير' %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="resultName" class="form-label">{% trans 'اسم النتيجة' %}</label>
                        <input type="text" class="form-control" id="resultName" name="name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans 'إلغاء' %}</button>
                    <button type="submit" class="btn btn-primary">{% trans 'حفظ' %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Saved Result Modal -->
<div class="modal fade" id="viewSavedResultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans 'عرض النتيجة المحفوظة' %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre class="bg-light p-3 rounded"><code id="savedResultData"></code></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();
        
        // Fade out alerts after 3 seconds
        $('.alert:not(.alert-danger)').delay(3000).fadeOut(500);
        
        // Handle viewing saved results
        $('.view-saved-result').click(function() {
            let data = $(this).data('result');
            $('#savedResultData').text(JSON.stringify(data, null, 2));
        });
    });
</script>
{% endblock %}
