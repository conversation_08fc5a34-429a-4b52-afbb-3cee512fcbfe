{% extends 'base.html' %}

{% block title %}الطلبات - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-shopping-cart"></i> الطلبات</h2>
        <a href="{% url 'orders:order_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> طلب جديد
        </a>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="بحث برقم الطلب أو اسم العميل" value="{{ search_query }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <select name="status" class="form-select">
                        <option value="">-- جميع الحالات --</option>
                        <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                        <option value="processing" {% if status_filter == 'processing' %}selected{% endif %}>قيد التنفيذ</option>
                        <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                        <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">تصفية</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders List -->
    <div class="card">
        <div class="card-header bg-light">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">قائمة الطلبات ({{ total_orders }})</h5>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>العميل</th>
                            <th>تاريخ الطلب</th>
                            <th>تاريخ التسليم</th>
                            <th>الحالة</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المبلغ المدفوع</th>
                            <th>المبلغ المتبقي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in page_obj %}
                        <tr>
                            <td>{{ order.order_number }}</td>
                            <td>{{ order.customer.name }}</td>
                            <td>{{ order.order_date|date:"Y-m-d" }}</td>
                            <td>{{ order.delivery_date|date:"Y-m-d"|default:"-" }}</td>
                            <td>
                                {% if order.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif order.status == 'processing' %}
                                <span class="badge bg-info">قيد التنفيذ</span>
                                {% elif order.status == 'completed' %}
                                <span class="badge bg-success">مكتمل</span>
                                {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% endif %}
                            </td>
                            <td>{{ order.total_amount }} {{ currency_symbol }}</td>
                            <td>{{ order.paid_amount }} {{ currency_symbol }}</td>
                            <td>{{ order.remaining_amount }} {{ currency_symbol }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'orders:order_detail' order.pk %}" class="btn btn-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'orders:order_update' order.pk %}" class="btn btn-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'orders:order_delete' order.pk %}" class="btn btn-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <p class="text-muted mb-0">لا توجد طلبات متاحة</p>
            </div>
            {% endif %}
        </div>
        {% if page_obj.has_other_pages %}
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                                {{ num }}
                            </a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
