{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة البائعين - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-users"></i> البائعون</h2>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="بحث باسم البائع أو رقمه الوظيفي..." value="{{ search_query }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                {% if user.is_superuser %}
                <div class="col-md-3">
                    <select name="branch" class="form-select">
                        <option value="">كل الفروع</option>
                        {% for branch in branches %}
                        <option value="{{ branch.id }}" {% if branch.id|stringformat:"s" == branch_filter %}selected{% endif %}>
                            {{ branch.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}
                <div class="col-md-3">
                    <select name="is_active" class="form-select">
                        <option value="">الجميع</option>
                        <option value="true" {% if is_active == "true" %}selected{% endif %}>نشط</option>
                        <option value="false" {% if is_active == "false" %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter"></i> تصفية
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Salespersons List -->
    <div class="card">
        <div class="card-header bg-light">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">قائمة البائعين ({{ total_salespersons }})</h5>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead>
                        <tr>
                            <th>الرقم الوظيفي</th>
                            <th>الاسم</th>
                            <th>الفرع</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for salesperson in page_obj %}
                        <tr>
                            <td>{{ salesperson.employee_number|default:"-" }}</td>
                            <td>{{ salesperson.name }}</td>
                            <td>{{ salesperson.branch.name }}</td>
                            <td>{{ salesperson.phone|default:"-" }}</td>
                            <td>{{ salesperson.email|default:"-" }}</td>
                            <td>
                                {% if salesperson.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <p class="text-muted mb-0">لا يوجد بائعون حالياً</p>
            </div>
            {% endif %}

            {% if page_obj.has_other_pages %}
            <div class="card-footer">
                <nav>
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if branch_filter %}&branch={{ branch_filter }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if branch_filter %}&branch={{ branch_filter }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if branch_filter %}&branch={{ branch_filter }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                                    {{ num }}
                                </a>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if branch_filter %}&branch={{ branch_filter }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if branch_filter %}&branch={{ branch_filter }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}