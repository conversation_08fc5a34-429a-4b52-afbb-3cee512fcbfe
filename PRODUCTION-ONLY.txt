========================================
    PRODUCTION SERVER - READ ONLY
========================================

⚠️  WARNING: This is a PRODUCTION-ONLY machine!

🚫 DO NOT:
- Make code changes on this machine
- Push changes to GitHub from here
- Modify core application files
- Delete production configuration files

✅ ALLOWED OPERATIONS:
- Run the website: run-elkhawaga.bat
- Update from repository: update-system.bat
- Monitor logs: update-log.txt
- Restart server when needed

🔄 UPDATE WORKFLOW:
1. Development machine makes changes
2. Development machine pushes to GitHub
3. This machine receives updates automatically (3 AM daily)
4. Or manually run: update-system.bat

🌐 LIVE WEBSITE:
- Main: https://elkhawaga.uk
- CRM: https://crm.elkhawaga.uk
- Admin: https://admin.elkhawaga.uk

📞 SUPPORT:
If you need to make changes, use the development machine
and push updates to the repository.

========================================
Last Update: $(date)
Repository: https://github.com/zakeetahawi/homeupdate
========================================
