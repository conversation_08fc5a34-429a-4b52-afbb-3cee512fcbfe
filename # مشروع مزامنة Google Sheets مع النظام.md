# مشروع مزامنة Google Sheets مع النظام

## الهدف

مزامنة بيانات العملاء والطلبات بين Google Sheets والنظام بشكل ديناميكي وتلقائي، مع دعم المزامنة العكسية.  
يهدف النظام إلى أتمتة عمليات الاستيراد والتحديث بحيث يتم إنشاء الطلبات، المعاينات، عمليات التركيب، وتحديث بيانات العملاء بشكل مباشر من الجدول، مع إمكانية تحديث الجدول نفسه بناءً على تغييرات النظام.

---

## الوظائف الأساسية

- استيراد بيانات العملاء والطلبات من Google Sheets.
- تعيين الأعمدة يدويًا من قبل المستخدم (مثلاً: رقم الفاتورة، موقف التركيب، اسم العميل...).
- **إنشاء الطلبات تلقائيًا** بعد الاستيراد.
- **إنشاء معاينات تلقائيًا** لكل طلب جديد.
- **إنشاء عمليات تركيب تلقائيًا** إذا تطلبت البيانات ذلك.
- **إدراج اسم العميل وبياناته** في النظام إذا لم يكن موجودًا.
- **تحديث الطلب الخاص بالعميل** تلقائيًا عند وجود تغييرات في الجدول.
- مراقبة الجدول بشكل دوري (كل دقيقة) لجلب التغييرات الجديدة.
- **مزامنة عكسية:** تحديث Google Sheets تلقائيًا عند حدوث تغييرات في النظام (مثل تغيير حالة الطلب أو بيانات العميل).
- معالجة التعارضات في حال التعديل من الطرفين (النظام والجدول).

---

## المتطلبات التقنية

- **النظام الأساسي:** Odoo أو Django (حسب إعداد النظام الحالي).
- **نظام استيراد Google Sheets:** موجود مسبقًا بالنظام وسيتم توسيعه.
- **بيانات اعتماد Google API:** مخزنة مسبقًا في النظام مع دعم ربط إيميل الخدمة.
- **مكتبة gspread أو Google Sheets API:** مستخدمة في النظام.
- **واجهة مستخدم ديناميكية:** لعرض الأعمدة وتعيينها من قبل المستخدم.
- **جدولة المهام الدورية:** Celery (مع Django) أو Scheduled Actions (مع Odoo).
- **قاعدة البيانات:** PostgreSQL أو حسب إعداد النظام.
- **نظام صلاحيات المستخدمين:** لضبط من يمكنه تعيين الأعمدة أو تفعيل المزامنة.
- **سجل مزامنة:** لحفظ آخر صف أو توقيت تمت معالجته لتفادي التكرار.
- **معالجة التعارضات:** في حال التعديل من الطرفين (النظام والجدول).

---

## خطوات العمل

1. **إعداد Google Sheets API:**
   - التأكد من صلاحية بيانات الاعتماد المخزنة وربطها بإيميل الخدمة المناسب.
   - التأكد من صلاحية الوصول للجدول الشامل الجديد.

2. **واجهة المستخدم:**
   - إضافة زر/أيقونة في صفحة الاستيراد الحالية (odoo-db-manager/google-import/).
   - عند اختيار جدول جديد، جلب الأعمدة تلقائيًا.
   - تعيين وظيفة كل عمود (مثلاً: رقم الفاتورة، اسم العميل، موقف التركيب...).
   - حفظ إعدادات التعيين لكل جدول.

3. **الاستيراد والمعالجة:**
   - قراءة البيانات من الجدول بناءً على التعيين.
   - **إنشاء أو تحديث بيانات العميل** في قاعدة البيانات.
   - **إنشاء طلب جديد** إذا لم يكن موجودًا.
   - **إنشاء معاينة تلقائيًا** مرتبطة بالطلب.
   - **إنشاء عملية تركيب تلقائيًا** إذا كان ذلك مطلوبًا من البيانات.
   - **تحديث الطلب الخاص بالعميل** تلقائيًا عند وجود تغييرات في الجدول.
   - تجاهل الأعمدة غير المعينة.

4. **المزامنة التلقائية:**
   - جدولة مهمة دورية (كل دقيقة) لمراقبة الجدول وجلب التغييرات الجديدة فقط.
   - حفظ آخر صف تمت معالجته أو توقيت آخر مزامنة.

5. **المزامنة العكسية:**
   - عند تحديث أو إنشاء طلب/عميل في النظام، يتم تحديث Google Sheet تلقائيًا.
   - معالجة التعارضات في حال التعديل من الطرفين.

6. **التوثيق والاختبار:**
   - توثيق جميع الخطوات والإعدادات.
   - اختبار جميع السيناريوهات (استيراد، تحديث، مزامنة عكسية، تعيين الأعمدة...).

---

## ملاحظات

- يجب معالجة التعارضات في حال التعديل من الطرفين.
- يفضل حفظ آخر توقيت مزامنة أو رقم الصف الأخير لتفادي التكرار.
- يمكن تخصيص الحقول حسب احتياج النظام.
- يجب اختبار جميع السيناريوهات قبل الإطلاق.
- يجب الالتزام بنظام الصلاحيات لضمان أمان البيانات.

---

## فريق العمل

- المطور: [اسمك]
- تاريخ البدء: [تاريخ البدء]