# Cloudflare Tunnel Configuration for elkhawaga.uk
# This file configures the tunnel to route traffic from your domain to your local Django app

# Tunnel configuration (will be updated after tunnel creation)
tunnel: 02d18311-1c6a-479a-a4c2-cc5750fb846e
credentials-file: ./cloudflare-credentials.json

# Ingress rules - defines how traffic is routed
ingress:
  # Main domain - routes to Django CRM application
  - hostname: elkhawaga.uk
    service: http://localhost:8000
    originRequest:
      httpHostHeader: elkhawaga.uk
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 100
      keepAliveTimeout: 90s

  # WWW subdomain - also routes to Django CRM application
  - hostname: www.elkhawaga.uk
    service: http://localhost:8000
    originRequest:
      httpHostHeader: www.elkhawaga.uk
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 100
      keepAliveTimeout: 90s

  # CRM subdomain - dedicated CRM access
  - hostname: crm.elkhawaga.uk
    service: http://localhost:8000
    originRequest:
      httpHostHeader: crm.elkhawaga.uk
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 100
      keepAliveTimeout: 90s

  # API subdomain - for API access
  - hostname: api.elkhawaga.uk
    service: http://localhost:8000
    originRequest:
      httpHostHeader: api.elkhawaga.uk
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 100
      keepAliveTimeout: 90s

  # Admin subdomain - for admin panel
  - hostname: admin.elkhawaga.uk
    service: http://localhost:8000
    originRequest:
      httpHostHeader: admin.elkhawaga.uk
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 100
      keepAliveTimeout: 90s

  # Catch-all rule - returns 404 for unmatched hostnames
  - service: http_status:404

# Optional: Tunnel-level configuration
warp-routing:
  enabled: false

# Logging configuration
loglevel: info

# Metrics server (optional) - disabled due to port conflict
# metrics: localhost:8081
