# Generated by Django 4.2.21 on 2025-07-02 13:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children', to='inventory.category', verbose_name='الفئة الأب')),
            ],
            options={
                'verbose_name': 'فئة',
                'verbose_name_plural': 'الفئات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='EGP', max_length=3, verbose_name='العملة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('minimum_stock', models.PositiveIntegerField(default=0, verbose_name='الحد الأدنى للمخزون')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='inventory.category', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'منتجات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الطلب')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('pending', 'قيد الانتظار'), ('approved', 'تمت الموافقة'), ('partial', 'استلام جزئي'), ('received', 'تم الاستلام'), ('cancelled', 'ملغي')], default='draft', max_length=10, verbose_name='الحالة')),
                ('order_date', models.DateField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('expected_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي المبلغ')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_purchase_orders', to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة')),
            ],
            options={
                'verbose_name': 'طلب شراء',
                'verbose_name_plural': 'طلبات الشراء',
                'ordering': ['-order_date'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المورد')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='جهة الاتصال')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('tax_number', models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المستودع')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المستودع')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouses', to='accounts.branch', verbose_name='الفرع')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_warehouses', to=settings.AUTH_USER_MODEL, verbose_name='المدير')),
            ],
            options={
                'verbose_name': 'مستودع',
                'verbose_name_plural': 'المستودعات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='WarehouseLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الموقع')),
                ('code', models.CharField(max_length=30, verbose_name='رمز الموقع')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='inventory.warehouse', verbose_name='المستودع')),
            ],
            options={
                'verbose_name': 'موقع مستودع',
                'verbose_name_plural': 'مواقع المستودعات',
                'ordering': ['warehouse', 'name'],
                'unique_together': {('warehouse', 'code')},
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('received_quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية المستلمة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_order_items', to='inventory.product', verbose_name='المنتج')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.purchaseorder', verbose_name='طلب الشراء')),
            ],
            options={
                'verbose_name': 'عنصر طلب الشراء',
                'verbose_name_plural': 'عناصر طلب الشراء',
                'ordering': ['purchase_order', 'product'],
            },
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='inventory.supplier', verbose_name='المورد'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='warehouse',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_orders', to='inventory.warehouse', verbose_name='المستودع'),
        ),
        migrations.CreateModel(
            name='ProductBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_number', models.CharField(max_length=50, verbose_name='رقم الدفعة')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('manufacturing_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التصنيع')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الصلاحية')),
                ('barcode', models.CharField(blank=True, max_length=100, verbose_name='الباركود')),
                ('cost_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='سعر التكلفة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='product_batches', to='inventory.warehouselocation', verbose_name='الموقع')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='batches', to='inventory.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'دفعة منتج',
                'verbose_name_plural': 'دفعات المنتجات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InventoryAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjustment_type', models.CharField(choices=[('increase', 'زيادة'), ('decrease', 'نقص')], max_length=10, verbose_name='نوع التسوية')),
                ('quantity_before', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية قبل')),
                ('quantity_after', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية بعد')),
                ('reason', models.TextField(verbose_name='سبب التسوية')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التسوية')),
                ('batch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='adjustments', to='inventory.productbatch', verbose_name='الدفعة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventory_adjustments', to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='adjustments', to='inventory.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'تسوية مخزون',
                'verbose_name_plural': 'تسويات المخزون',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='StockTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('in', 'وارد'), ('out', 'صادر'), ('transfer', 'نقل'), ('adjustment', 'تسوية')], max_length=10, verbose_name='نوع الحركة')),
                ('reason', models.CharField(choices=[('purchase', 'شراء'), ('sale', 'بيع'), ('return', 'مرتجع'), ('transfer', 'نقل'), ('inventory_check', 'جرد'), ('damage', 'تلف'), ('production', 'إنتاج'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='السبب')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('reference', models.CharField(blank=True, max_length=100, verbose_name='المرجع')),
                ('transaction_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ العملية')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التسجيل')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('running_balance', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد المتحرك')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_transactions', to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='inventory.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-transaction_date', '-date'],
                'indexes': [models.Index(fields=['product'], name='transaction_product_idx'), models.Index(fields=['transaction_type'], name='transaction_type_idx'), models.Index(fields=['transaction_date'], name='transaction_date_idx')],
            },
        ),
        migrations.CreateModel(
            name='StockAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('low_stock', 'مخزون منخفض'), ('expiry', 'قرب انتهاء الصلاحية'), ('out_of_stock', 'نفاد المخزون'), ('overstock', 'فائض في المخزون'), ('price_change', 'تغير في السعر')], max_length=15, verbose_name='نوع التنبيه')),
                ('message', models.TextField(verbose_name='رسالة التنبيه')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('priority', models.CharField(choices=[('high', 'عالية'), ('medium', 'متوسطة'), ('low', 'منخفضة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('resolved', 'تمت المعالجة'), ('ignored', 'تم تجاهله')], default='active', max_length=10, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ المعالجة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_alerts', to='inventory.product', verbose_name='المنتج')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_stock_alerts', to=settings.AUTH_USER_MODEL, verbose_name='تمت المعالجة بواسطة')),
            ],
            options={
                'verbose_name': 'تنبيه مخزون',
                'verbose_name_plural': 'تنبيهات المخزون',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['product'], name='alert_product_idx'), models.Index(fields=['alert_type'], name='alert_type_idx'), models.Index(fields=['status'], name='alert_status_idx'), models.Index(fields=['priority'], name='alert_priority_idx'), models.Index(fields=['created_at'], name='alert_created_at_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['order_number'], name='po_number_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['supplier'], name='po_supplier_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['status'], name='po_status_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaseorder',
            index=models.Index(fields=['order_date'], name='po_date_idx'),
        ),
        migrations.AddIndex(
            model_name='productbatch',
            index=models.Index(fields=['product'], name='batch_product_idx'),
        ),
        migrations.AddIndex(
            model_name='productbatch',
            index=models.Index(fields=['batch_number'], name='batch_number_idx'),
        ),
        migrations.AddIndex(
            model_name='productbatch',
            index=models.Index(fields=['expiry_date'], name='batch_expiry_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['name', 'code'], name='inventory_p_name_af55cc_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category'], name='inventory_p_categor_607069_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['created_at'], name='inventory_p_created_081871_idx'),
        ),
        migrations.AddIndex(
            model_name='inventoryadjustment',
            index=models.Index(fields=['product'], name='adjustment_product_idx'),
        ),
        migrations.AddIndex(
            model_name='inventoryadjustment',
            index=models.Index(fields=['adjustment_type'], name='adjustment_type_idx'),
        ),
        migrations.AddIndex(
            model_name='inventoryadjustment',
            index=models.Index(fields=['date'], name='adjustment_date_idx'),
        ),
    ]
