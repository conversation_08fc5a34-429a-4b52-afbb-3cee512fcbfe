{% extends 'base.html' %}
{% block title %}لوحة تحكم المخزون{% endblock %}
{% block content %}
<div class="container">
    <h2 class="my-4">لوحة تحكم المخزون</h2>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">إجراءات سريعة</h5>
                    <div class="btn-group" role="group">
                        <a href="{% url 'inventory:product_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة منتج جديد
                        </a>
                        <a href="{% url 'inventory:product_list' %}" class="btn btn-info">
                            <i class="fas fa-list"></i> قائمة المنتجات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">إجمالي المنتجات</h5>
                    <p class="display-6">{{ total_products }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">منتجات منخفضة المخزون</h5>
                    <p class="display-6 text-danger">{{ low_stock_products_count }}</p>
                    {% if low_stock_products_count > 0 %}
                        <a href="{% url 'inventory:product_list' %}?filter=low_stock" class="btn btn-danger btn-sm">عرض التفاصيل</a>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title">أصناف</h5>
                    <p class="display-6">{{ total_categories }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Products -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0">أحدث المنتجات</h4>
            <a href="{% url 'inventory:product_list' %}" class="btn btn-primary btn-sm">
                عرض الكل
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الوصف</th>
                            <th>المخزون</th>
                            <th>الحد الأدنى</th>
                            <th>التصنيف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in recent_products %}
                        <tr>
                            <td>{{ product.name }}</td>
                            <td>{{ product.description }}</td>
                            <td>
                                <span class="{% if product.stock < product.minimum_stock %}text-danger{% endif %}">
                                    {{ product.stock }}
                                </span>
                            </td>
                            <td>{{ product.minimum_stock }}</td>
                            <td>{{ product.category }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'inventory:product_detail' product.id %}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:product_update' product.id %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'inventory:transaction_create' product.id %}" class="btn btn-success btn-sm">
                                        <i class="fas fa-exchange-alt"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr><td colspan="6" class="text-center">لا يوجد منتجات حديثاً.</td></tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
