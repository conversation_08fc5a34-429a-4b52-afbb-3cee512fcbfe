{% extends 'inventory/inventory_base.html' %}
{% load static %}

{% block inventory_title %}الموردين{% endblock %}

{% block inventory_content %}
<div class="supplier-list-container">
    <!-- بطاقة إضافة مورد جديد -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">إضافة مورد جديد</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'inventory:supplier_create' %}">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم المورد</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="contact_person" class="form-label">الشخص المسؤول</label>
                                    <input type="text" class="form-control" id="contact_person" name="contact_person">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control" id="phone" name="phone">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="website" class="form-label">الموقع الإلكتروني</label>
                                    <input type="url" class="form-control" id="website" name="website">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="category" class="form-label">الفئة</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">اختر الفئة</option>
                                        <option value="local">محلي</option>
                                        <option value="international">دولي</option>
                                        <option value="manufacturer">مصنع</option>
                                        <option value="distributor">موزع</option>
                                        <option value="wholesaler">تاجر جملة</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">نشط</label>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة مورد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الموردين -->
    <div class="row">
        <div class="col-md-12">
            <div class="data-table">
                <div class="data-table-header">
                    <h4 class="data-table-title">
                        الموردين
                        {% if suppliers %}
                        <span class="badge bg-primary">{{ suppliers|length }}</span>
                        {% endif %}
                    </h4>
                    <div class="data-table-actions">
                        <div class="input-group" style="width: 300px;">
                            <input type="text" class="form-control" id="supplierSearch" placeholder="بحث عن مورد...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="data-table-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover datatable">
                            <thead>
                                <tr>
                                    <th>اسم المورد</th>
                                    <th>الشخص المسؤول</th>
                                    <th>رقم الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الفئة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for supplier in suppliers %}
                                <tr>
                                    <td>
                                        <strong>{{ supplier.name }}</strong>
                                    </td>
                                    <td>{{ supplier.contact_person|default:"-" }}</td>
                                    <td>{{ supplier.phone|default:"-" }}</td>
                                    <td>{{ supplier.email|default:"-" }}</td>
                                    <td>
                                        {% if supplier.category == 'local' %}
                                        <span class="badge bg-info">محلي</span>
                                        {% elif supplier.category == 'international' %}
                                        <span class="badge bg-primary">دولي</span>
                                        {% elif supplier.category == 'manufacturer' %}
                                        <span class="badge bg-success">مصنع</span>
                                        {% elif supplier.category == 'distributor' %}
                                        <span class="badge bg-warning">موزع</span>
                                        {% elif supplier.category == 'wholesaler' %}
                                        <span class="badge bg-secondary">تاجر جملة</span>
                                        {% else %}
                                        <span class="badge bg-light text-dark">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if supplier.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                        {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'inventory:supplier_update' supplier.id %}" class="btn btn-primary btn-sm" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'inventory:supplier_delete' supplier.id %}" class="btn btn-danger btn-sm" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            <a href="{% url 'inventory:supplier_detail' supplier.id %}" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'inventory:purchase_order_list' %}?supplier={{ supplier.id }}" class="btn btn-success btn-sm" title="طلبات الشراء">
                                                <i class="fas fa-shopping-cart"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">لا يوجد موردين مضافين</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الموردين -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="chart-container">
                <div class="chart-header">
                    <h4 class="chart-title">توزيع الموردين حسب الفئة</h4>
                </div>
                <div class="chart-body">
                    <canvas id="supplierCategoryChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <div class="chart-header">
                    <h4 class="chart-title">أكثر 5 موردين نشاطاً</h4>
                </div>
                <div class="chart-body">
                    <canvas id="topSuppliersChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة جدول البيانات
        $('.datatable').DataTable({
            language: {
                processing: "جارٍ التحميل...",
                search: "بحث:",
                lengthMenu: "عرض _MENU_ سجلات",
                info: "عرض _START_ إلى _END_ من أصل _TOTAL_ سجل",
                infoEmpty: "عرض 0 إلى 0 من أصل 0 سجل",
                infoFiltered: "(منتقاة من مجموع _MAX_ سجل)",
                infoPostFix: "",
                loadingRecords: "جارٍ التحميل...",
                zeroRecords: "لم يعثر على أية سجلات",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                paginate: {
                    first: "الأول",
                    previous: "السابق",
                    next: "التالي",
                    last: "الأخير"
                },
                aria: {
                    sortAscending: ": تفعيل لترتيب العمود تصاعدياً",
                    sortDescending: ": تفعيل لترتيب العمود تنازلياً"
                }
            },
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            responsive: true,
            autoWidth: false
        });
        
        // رسم بياني لتوزيع الموردين حسب الفئة
        const categoryCtx = document.getElementById('supplierCategoryChart').getContext('2d');
        const supplierCategoryChart = new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: ['محلي', 'دولي', 'مصنع', 'موزع', 'تاجر جملة', 'غير محدد'],
                datasets: [{
                    data: [
                        {{ suppliers|filter:"category='local'"|length }},
                        {{ suppliers|filter:"category='international'"|length }},
                        {{ suppliers|filter:"category='manufacturer'"|length }},
                        {{ suppliers|filter:"category='distributor'"|length }},
                        {{ suppliers|filter:"category='wholesaler'"|length }},
                        {{ suppliers|filter:"category=''"|length }}
                    ],
                    backgroundColor: [
                        '#36b9cc', '#4e73df', '#1cc88a', '#f6c23e', '#6c757d', '#e3e6f0'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        rtl: true
                    },
                    tooltip: {
                        rtl: true,
                        callbacks: {
                            label: function(tooltipItem) {
                                const dataset = tooltipItem.dataset;
                                const total = dataset.data.reduce((acc, val) => acc + val, 0);
                                const currentValue = dataset.data[tooltipItem.dataIndex];
                                const percentage = Math.round((currentValue / total) * 100);
                                return `${tooltipItem.label}: ${currentValue} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
        
        // رسم بياني لأكثر 5 موردين نشاطاً
        const topSuppliersCtx = document.getElementById('topSuppliersChart').getContext('2d');
        const topSuppliersChart = new Chart(topSuppliersCtx, {
            type: 'bar',
            data: {
                labels: [{% for supplier in top_suppliers %}'{{ supplier.name }}',{% endfor %}],
                datasets: [{
                    label: 'عدد طلبات الشراء',
                    data: [{% for supplier in top_suppliers %}{{ supplier.orders_count }},{% endfor %}],
                    backgroundColor: '#4e73df',
                    borderColor: '#4e73df',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    });
</script>
{% endblock %}
