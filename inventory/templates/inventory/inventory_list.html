{% extends 'base.html' %}
{% load math_filters_inventory %}

{% block title %}إدارة المخزون - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">إدارة المخزون</h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'inventory:product_create' %}" class="btn" style="background-color: var(--primary); color: white;">
                <i class="fas fa-plus"></i> إضافة منتج جديد
            </a>
        </div>
    </div>

    <!-- Inventory Stats -->
    <div class="row mb-4">
        <div class="col-md-4 mb-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-boxes fa-3x mb-3" style="color: var(--primary);"></i>
                    <h5 class="card-title">إجمالي المنتجات</h5>
                    <p class="card-text display-6">{{ total_products }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3" style="color: var(--alert);"></i>
                    <h5 class="card-title">منتجات منخفضة</h5>
                    <p class="card-text display-6">{{ low_stock_count }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-truck fa-3x mb-3" style="color: var(--light-accent);"></i>
                    <h5 class="card-title">طلبات التوريد</h5>
                    <p class="card-text display-6">{{ purchase_orders_count }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4" style="border-color: var(--neutral);">
        <div class="card-body">
            <form method="get" action="{% url 'inventory:inventory_list' %}">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="البحث عن منتج..." value="{{ search_query }}">
                            <button type="submit" class="btn" style="background-color: var(--primary); color: white;">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <select name="category" class="form-select">
                            <option value="">جميع الفئات</option>
                            {% for category in categories %}
                                <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"i" %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="in_stock" {% if status_filter == 'in_stock' %}selected{% endif %}>متوفر</option>
                            <option value="low_stock" {% if status_filter == 'low_stock' %}selected{% endif %}>منخفض</option>
                            <option value="out_of_stock" {% if status_filter == 'out_of_stock' %}selected{% endif %}>غير متوفر</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="submit" class="btn w-100" style="background-color: var(--primary); color: white;">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Products List -->
    <div class="card" style="border-color: var(--neutral);">
        <div class="card-header" style="background-color: var(--primary); color: white;">
            <h5 class="mb-0"><i class="fas fa-boxes"></i> قائمة المنتجات</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>اسم المنتج</th>
                            <th>الفئة</th>
                            <th>المخزون الحالي</th>
                            <th>الحد الأدنى</th>
                            <th>سعر الوحدة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in page_obj %}
                        <tr>
                            <td>{{ product.code }}</td>
                            <td>{{ product.name }}</td>
                            <td>{% if product.category %}{{ product.category.name }}{% else %}-{% endif %}</td>
                            <td>{{ product.current_stock }}</td>
                            <td>{{ product.minimum_stock }}</td>
                            <td>{{ product.price }} {{ currency_symbol }}</td>
                            <td>
                                {% if product.current_stock <= 0 %}
                                    <span class="badge bg-secondary">غير متوفر</span>
                                {% elif product.needs_restock %}
                                    <span class="badge" style="background-color: var(--alert); color: white;">منخفض</span>
                                {% else %}
                                    <span class="badge bg-success">متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'inventory:product_detail' product.pk %}" class="btn btn-sm" style="background-color: var(--primary); color: white;" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:product_update' product.pk %}" class="btn btn-sm" style="background-color: var(--light-accent); color: var(--dark-text);" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'inventory:product_delete' product.pk %}" class="btn btn-sm" style="background-color: var(--alert); color: white;" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center py-4">لا توجد منتجات متاحة</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                            <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                        {% else %}
                            <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ i }}</a></li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
