{% extends 'inventory/inventory_base.html' %}
{% load static %}

{% block inventory_title %}لوحة تحكم المخزون{% endblock %}

{% block inventory_content %}
<div class="dashboard-container">
    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="stat-card primary">
                <div class="stat-card-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-card-value">{{ total_products }}</div>
                <div class="stat-card-title">إجمالي المنتجات</div>
                <div class="stat-card-footer">
                    <a href="{% url 'inventory:product_list' %}">عرض التفاصيل <i class="fas fa-arrow-left"></i></a>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card danger">
                <div class="stat-card-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-card-value">{{ low_stock_products_count }}</div>
                <div class="stat-card-title">منتجات منخفضة المخزون</div>
                <div class="stat-card-footer">
                    <a href="{% url 'inventory:product_list' %}?filter=low_stock">عرض التفاصيل <i class="fas fa-arrow-left"></i></a>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card success">
                <div class="stat-card-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="stat-card-value">{{ total_categories }}</div>
                <div class="stat-card-title">التصنيفات</div>
                <div class="stat-card-footer">
                    <a href="#">عرض التفاصيل <i class="fas fa-arrow-left"></i></a>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card warning">
                <div class="stat-card-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-card-value">{{ pending_purchase_orders }}</div>
                <div class="stat-card-title">طلبات شراء معلقة</div>
                <div class="stat-card-footer">
                    <a href="#">عرض التفاصيل <i class="fas fa-arrow-left"></i></a>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
        <div class="col-xl-6">
            <div class="chart-container">
                <div class="chart-header">
                    <h4 class="chart-title">المخزون حسب الفئة</h4>
                    <div class="chart-actions">
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                    </div>
                </div>
                <div class="chart-body">
                    <canvas id="stockByCategoryChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="chart-container">
                <div class="chart-header">
                    <h4 class="chart-title">حركة المخزون (آخر 30 يوم)</h4>
                    <div class="chart-actions">
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                    </div>
                </div>
                <div class="chart-body">
                    <canvas id="stockMovementChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- المنتجات منخفضة المخزون -->
    <div class="row">
        <div class="col-12">
            <div class="data-table">
                <div class="data-table-header">
                    <h4 class="data-table-title">المنتجات منخفضة المخزون</h4>
                    <div class="data-table-actions">
                        <a href="{% url 'inventory:product_list' %}?filter=low_stock" class="btn btn-primary btn-sm">
                            عرض الكل
                        </a>
                    </div>
                </div>
                <div class="data-table-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكود</th>
                                    <th>الفئة</th>
                                    <th>المخزون الحالي</th>
                                    <th>الحد الأدنى</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_products %}
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.code }}</td>
                                    <td>{{ product.category }}</td>
                                    <td>
                                        <span class="badge badge-danger">{{ product.stock }}</span>
                                    </td>
                                    <td>{{ product.minimum_stock }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'inventory:product_detail' product.id %}" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'inventory:transaction_create' product.id %}" class="btn btn-success btn-sm">
                                                <i class="fas fa-plus"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">لا توجد منتجات منخفضة المخزون</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر حركات المخزون -->
    <div class="row">
        <div class="col-12">
            <div class="data-table">
                <div class="data-table-header">
                    <h4 class="data-table-title">آخر حركات المخزون</h4>
                    <div class="data-table-actions">
                        <a href="{% url 'inventory:stock_movement_report' %}" class="btn btn-primary btn-sm">
                            عرض الكل
                        </a>
                    </div>
                </div>
                <div class="data-table-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المنتج</th>
                                    <th>نوع الحركة</th>
                                    <th>الكمية</th>
                                    <th>السبب</th>
                                    <th>بواسطة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.date|date:"Y-m-d H:i" }}</td>
                                    <td>{{ transaction.product.name }}</td>
                                    <td>
                                        <span class="badge {% if transaction.transaction_type == 'in' %}badge-success{% elif transaction.transaction_type == 'out' %}badge-danger{% else %}badge-info{% endif %}">
                                            {{ transaction.get_transaction_type_display }}
                                        </span>
                                    </td>
                                    <td>{{ transaction.quantity }}</td>
                                    <td>{{ transaction.get_reason_display }}</td>
                                    <td>{{ transaction.created_by.get_full_name }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">لا توجد حركات مخزون حديثة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    // بيانات الرسوم البيانية
    const stockByCategoryData = {
        labels: [{% for category in stock_by_category %}'{{ category.name }}',{% endfor %}],
        data: [{% for category in stock_by_category %}{{ category.stock }},{% endfor %}]
    };
    
    const stockMovementData = {
        labels: [{% for date in stock_movement_dates %}'{{ date|date:"d/m" }}',{% endfor %}],
        inData: [{% for value in stock_movement_in %}{{ value }},{% endfor %}],
        outData: [{% for value in stock_movement_out %}{{ value }},{% endfor %}]
    };
</script>
{% endblock %}
