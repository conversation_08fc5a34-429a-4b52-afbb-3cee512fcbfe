{% extends 'inventory/inventory_base_custom.html' %}
{% load static %}

{% block inventory_title %}تفاصيل المنتج: {{ product.name }}{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">لوحة التحكم</a></li>
<li class="breadcrumb-item"><a href="{% url 'inventory:product_list' %}">المنتجات</a></li>
<li class="breadcrumb-item active" aria-current="page">{{ product.name }}</li>
{% endblock %}

{% block quick_actions %}
<a href="{% url 'inventory:product_update' product.id %}" class="btn btn-primary btn-sm">
    <i class="fas fa-edit"></i> تعديل
</a>
<a href="{% url 'inventory:transaction_create' product.id %}" class="btn btn-success btn-sm">
    <i class="fas fa-exchange-alt"></i> إضافة حركة
</a>
<a href="{% url 'inventory:product_delete' product.id %}" class="btn btn-danger btn-sm">
    <i class="fas fa-trash"></i> حذف
</a>
{% endblock %}

{% block inventory_content %}
<div class="product-detail-container">
    <!-- بطاقة تفاصيل المنتج -->
    <div class="row">
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">معلومات المنتج</h5>
                </div>
                <div class="card-body">
                    <div class="product-info">
                        <div class="product-image">
                            <i class="fas fa-box fa-5x text-primary"></i>
                        </div>
                        <h3 class="product-name">{{ product.name }}</h3>
                        <div class="product-code">{{ product.code }}</div>
                        <div class="product-category">
                            <span class="badge bg-info">{{ product.category }}</span>
                        </div>
                        <div class="product-price mt-3">
                            <h4>{{ product.price }} {{ currency_symbol }}</h4>
                        </div>
                    </div>
                    <hr>
                    <div class="product-description">
                        <h6>الوصف:</h6>
                        <p>{{ product.description|default:"لا يوجد وصف" }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-8">
            <div class="row">
                <!-- بطاقة حالة المخزون -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">حالة المخزون</h5>
                        </div>
                        <div class="card-body">
                            <div class="stock-status text-center">
                                <div class="stock-gauge">
                                    {% if current_stock <= 0 %}
                                    <div class="gauge-value danger">{{ current_stock }}</div>
                                    <div class="gauge-label">نفذ من المخزون</div>
                                    {% elif current_stock <= product.minimum_stock %}
                                    <div class="gauge-value warning">{{ current_stock }}</div>
                                    <div class="gauge-label">مخزون منخفض</div>
                                    {% else %}
                                    <div class="gauge-value success">{{ current_stock }}</div>
                                    <div class="gauge-label">متوفر</div>
                                    {% endif %}
                                </div>
                                <div class="stock-details mt-3">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="detail-label">الحد الأدنى</div>
                                            <div class="detail-value">{{ product.minimum_stock }}</div>
                                        </div>
                                        <div class="col-6">
                                            <div class="detail-label">الحالة</div>
                                            <div class="detail-value">
                                                {% if current_stock <= 0 %}
                                                <span class="badge bg-danger">{{ stock_status }}</span>
                                                {% elif current_stock <= product.minimum_stock %}
                                                <span class="badge bg-warning">{{ stock_status }}</span>
                                                {% else %}
                                                <span class="badge bg-success">{{ stock_status }}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- بطاقة الإحصائيات -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">إحصائيات</h5>
                        </div>
                        <div class="card-body">
                            <div class="stats-item">
                                <div class="stats-label">تاريخ الإضافة</div>
                                <div class="stats-value">{{ product.created_at|date:"Y-m-d" }}</div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-label">آخر تحديث</div>
                                <div class="stats-value">{{ product.updated_at|date:"Y-m-d" }}</div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-label">عدد الحركات</div>
                                <div class="stats-value">{{ transactions|length }}</div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-label">إجمالي الوارد</div>
                                <div class="stats-value text-success">
                                    {{ transactions_in_total }}
                                </div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-label">إجمالي الصادر</div>
                                <div class="stats-value text-danger">
                                    {{ transactions_out_total }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- بطاقة الرسم البياني -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">حركة المخزون</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="stockMovementChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- جدول حركات المخزون -->
    <div class="data-table-container">
        <div class="data-table-header">
            <h5 class="data-table-title">حركات المخزون</h5>
            <div class="data-table-actions">
                <a href="{% url 'inventory:transaction_create' product.id %}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> إضافة حركة
                </a>
            </div>
        </div>
        <div class="data-table-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>نوع الحركة</th>
                            <th>الكمية</th>
                            <th>السبب</th>
                            <th>المرجع</th>
                            <th>بواسطة</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                        <tr>
                            <td>{{ transaction.date|date:"Y-m-d H:i" }}</td>
                            <td>
                                <span class="badge {% if transaction.transaction_type == 'in' %}bg-success{% elif transaction.transaction_type == 'out' %}bg-danger{% else %}bg-info{% endif %}">
                                    {{ transaction.get_transaction_type_display }}
                                </span>
                            </td>
                            <td>{{ transaction.quantity }}</td>
                            <td>{{ transaction.get_reason_display }}</td>
                            <td>{{ transaction.reference|default:"-" }}</td>
                            <td>{{ transaction.created_by.get_full_name }}</td>
                            <td>{{ transaction.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">لا توجد حركات مخزون لهذا المنتج</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // بيانات الرسم البياني
        const ctx = document.getElementById('stockMovementChart').getContext('2d');
        const stockMovementChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [{% for date in transaction_dates %}'{{ date|date:"d/m" }}',{% endfor %}],
                datasets: [
                    {
                        label: 'الرصيد',
                        data: [{% for balance in transaction_balances %}{{ balance }},{% endfor %}],
                        borderColor: '#4e73df',
                        backgroundColor: 'rgba(78, 115, 223, 0.1)',
                        borderWidth: 2,
                        pointBackgroundColor: '#4e73df',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            zeroLineColor: 'rgba(0, 0, 0, 0.1)'
                        },
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        rtl: true
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        rtl: true
                    }
                }
            }
        });
        
        // تهيئة جدول البيانات
        $('.datatable').DataTable({
            language: {
                processing: "جارٍ التحميل...",
                search: "بحث:",
                lengthMenu: "عرض _MENU_ سجلات",
                info: "عرض _START_ إلى _END_ من أصل _TOTAL_ سجل",
                infoEmpty: "عرض 0 إلى 0 من أصل 0 سجل",
                infoFiltered: "(منتقاة من مجموع _MAX_ سجل)",
                infoPostFix: "",
                loadingRecords: "جارٍ التحميل...",
                zeroRecords: "لم يعثر على أية سجلات",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                paginate: {
                    first: "الأول",
                    previous: "السابق",
                    next: "التالي",
                    last: "الأخير"
                },
                aria: {
                    sortAscending: ": تفعيل لترتيب العمود تصاعدياً",
                    sortDescending: ": تفعيل لترتيب العمود تنازلياً"
                }
            },
            order: [[0, 'desc']],
            pageLength: 10
        });
    });
</script>
{% endblock %}
