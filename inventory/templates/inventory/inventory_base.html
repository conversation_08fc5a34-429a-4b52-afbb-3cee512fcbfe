{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/inventory-dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="inventory-dashboard">
    <!-- القائمة الجانبية -->
    <div class="inventory-sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-warehouse"></i> إدارة المخزون</h3>
        </div>
        <div class="sidebar-user">
            <div class="user-avatar">
                <i class="fas fa-user-circle"></i>
            </div>
            <div class="user-info">
                <h5>{{ request.user.get_full_name }}</h5>
                <span>{{ request.user.get_role_display }}</span>
            </div>
        </div>
        <ul class="sidebar-menu">
            <li class="{% if active_menu == 'dashboard' %}active{% endif %}">
                <a href="{% url 'inventory:dashboard' %}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>
            <li class="menu-header">المنتجات</li>
            <li class="{% if active_menu == 'products' %}active{% endif %}">
                <a href="{% url 'inventory:product_list' %}">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </a>
            </li>
            <li class="{% if active_menu == 'categories' %}active{% endif %}">
                <a href="{% url 'inventory:category_list' %}">
                    <i class="fas fa-tags"></i>
                    <span>التصنيفات</span>
                </a>
            </li>
            <li class="menu-header">المخزون</li>
            <li class="{% if active_menu == 'stock_transactions' %}active{% endif %}">
                <a href="{% url 'inventory:transaction_list' %}">
                    <i class="fas fa-exchange-alt"></i>
                    <span>حركات المخزون</span>
                </a>
            </li>
            <li class="{% if active_menu == 'stock_adjustments' %}active{% endif %}">
                <a href="{% url 'inventory:adjustment_list' %}">
                    <i class="fas fa-balance-scale"></i>
                    <span>تسويات المخزون</span>
                </a>
            </li>
            <li class="menu-header">المشتريات</li>
            <li class="{% if active_menu == 'purchase_orders' %}active{% endif %}">
                <a href="{% url 'inventory:purchase_order_list' %}">
                    <i class="fas fa-shopping-cart"></i>
                    <span>طلبات الشراء</span>
                </a>
            </li>
            <li class="{% if active_menu == 'suppliers' %}active{% endif %}">
                <a href="{% url 'inventory:supplier_list' %}">
                    <i class="fas fa-truck"></i>
                    <span>الموردين</span>
                </a>
            </li>
            <li class="menu-header">المستودعات</li>
            <li class="{% if active_menu == 'warehouses' %}active{% endif %}">
                <a href="{% url 'inventory:warehouse_list' %}">
                    <i class="fas fa-warehouse"></i>
                    <span>المستودعات</span>
                </a>
            </li>
            <li class="{% if active_menu == 'warehouse_locations' %}active{% endif %}">
                <a href="{% url 'inventory:warehouse_location_list' %}">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>مواقع التخزين</span>
                </a>
            </li>
            <li class="menu-header">التقارير</li>
            <li class="{% if active_menu == 'inventory_reports' %}active{% endif %}">
                <a href="{% url 'inventory:report_list' %}">
                    <i class="fas fa-chart-bar"></i>
                    <span>تقارير المخزون</span>
                </a>
            </li>
            <li class="{% if active_menu == 'stock_alerts' %}active{% endif %}">
                <a href="{% url 'inventory:alert_list' %}">
                    <i class="fas fa-bell"></i>
                    <span>تنبيهات المخزون</span>
                    {% if alerts_count > 0 %}
                    <span class="badge bg-danger">{{ alerts_count }}</span>
                    {% endif %}
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="inventory-content">
        <!-- شريط العنوان -->
        <div class="content-header">
            <div class="header-title">
                <h1>{% block inventory_title %}لوحة تحكم المخزون{% endblock %}</h1>
            </div>
            <div class="header-actions">
                <div class="search-box">
                    <form action="{% url 'inventory:product_list' %}" method="get">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="بحث عن منتج...">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                <div class="action-buttons">
                    <a href="{% url 'inventory:product_create' %}" class="btn btn-success">
                        <i class="fas fa-plus"></i> منتج جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- محتوى الصفحة -->
        <div class="content-body">
            {% block inventory_content %}{% endblock %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/inventory-dashboard.js' %}"></script>
{% endblock %}
