/**
 * Modern Black Theme - تحسينات إضافية
 * تحسينات للثيم الأسود الحديث مع ضمان وضوح النصوص والحدود
 */

/* تحسين z-index للقوائم المنسدلة */
[data-theme="modern-black"] .dropdown-menu,
[data-theme="modern-black"] .dropdown-item {
    z-index: 2147483647 !important; /* أقصى قيمة يمكن استخدامها */
}

/* ضمان أن القوائم المنسدلة تظهر فوق العناصر الأخرى */
[data-theme="modern-black"] .dropdown {
    position: relative !important;
    z-index: 2147483646 !important; /* قيمة عالية جداً */
    isolation: isolate !important; /* عزل طبقي للعناصر */
}

/* تضمين أن جميع النصوص تكون بيضاء أو زرقاء فاتحة لضمان الرؤية */
[data-theme="modern-black"] .card,
[data-theme="modern-black"] .modal-content,
[data-theme="modern-black"] .list-group-item,
[data-theme="modern-black"] .table th,
[data-theme="modern-black"] .table td,
[data-theme="modern-black"] input,
[data-theme="modern-black"] select,
[data-theme="modern-black"] textarea,
[data-theme="modern-black"] .breadcrumb,
[data-theme="modern-black"] .breadcrumb-item,
[data-theme="modern-black"] .text-muted,
[data-theme="modern-black"] .text-secondary,
[data-theme="modern-black"] .text-dark,
[data-theme="modern-black"] .form-text,
[data-theme="modern-black"] .form-label,
[data-theme="modern-black"] .small,
[data-theme="modern-black"] small,
[data-theme="modern-black"] .page-link {
    color: var(--text-primary) !important;
    border-color: var(--primary) !important;
}

/* تحسين الحدود لجميع العناصر */
[data-theme="modern-black"] .card,
[data-theme="modern-black"] .modal-content,
[data-theme="modern-black"] .list-group-item,
[data-theme="modern-black"] .dropdown-menu,
[data-theme="modern-black"] input,
[data-theme="modern-black"] select,
[data-theme="modern-black"] textarea,
[data-theme="modern-black"] .form-control {
    border: 1px solid var(--primary) !important;
}

/* تحسين مخطط الصفحة (Breadcrumbs) */
[data-theme="modern-black"] .breadcrumb,
[data-theme="modern-black"] .breadcrumb-item,
[data-theme="modern-black"] .breadcrumb-item.active,
[data-theme="modern-black"] .breadcrumb a,
[data-theme="modern-black"] ol.breadcrumb li,
[data-theme="modern-black"] .breadcrumb-item a,
[data-theme="modern-black"] nav[aria-label="breadcrumb"] *,
[data-theme="modern-black"] .page-header .breadcrumb {
    color: #ffffff !important;
    text-shadow: 0 0 3px rgba(0, 210, 255, 0.5) !important;
    font-weight: 500 !important;
}

/* تحسين الرابط النشط في مخطط الصفحة */
[data-theme="modern-black"] .breadcrumb-item.active {
    color: #00D2FF !important;
    font-weight: bold !important;
}

[data-theme="modern-black"] .breadcrumb-item+.breadcrumb-item::before {
    color: #00D2FF !important;
}

/* إصلاح مشكلة تداخل القوائم مع البطاقات */
[data-theme="modern-black"] .card,
[data-theme="modern-black"] .content-card,
[data-theme="modern-black"] .dashboard-card,
[data-theme="modern-black"] .card-body,
[data-theme="modern-black"] .card-header,
[data-theme="modern-black"] .card-footer {
    z-index: 1 !important;
    position: relative !important;
    transform: translateZ(0) !important; /* إبقاء البطاقات في مستوى Z صفر */
}

[data-theme="modern-black"] .dropdown {
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
    will-change: transform; /* تحسين الأداء */
}

[data-theme="modern-black"] .dropdown-menu {
    transform: translateZ(999999999px) !important;
    -webkit-transform: translateZ(999999999px) !important;
    will-change: transform; /* تحسين الأداء */
    -webkit-transform-style: preserve-3d !important; /* دعم للمتصفحات القديمة */
    transform-style: preserve-3d !important;
    backface-visibility: hidden !important; /* منع المشاكل البصرية */
}

/* تحسين نماذج إضافة العملاء والمدخلات */
[data-theme="modern-black"] label,
[data-theme="modern-black"] .form-label,
[data-theme="modern-black"] .form-check-label,
[data-theme="modern-black"] .form-text,
[data-theme="modern-black"] .help-text,
[data-theme="modern-black"] .hint-text,
[data-theme="modern-black"] .form-control-plaintext,
[data-theme="modern-black"] legend,
[data-theme="modern-black"] .col-form-label {
    color: #ffffff !important;
    font-weight: 500 !important;
}

/* تحسين وصف الحقول والنصوص المساعدة */
[data-theme="modern-black"] .form-text,
[data-theme="modern-black"] .text-muted,
[data-theme="modern-black"] .text-secondary,
[data-theme="modern-black"] .text-black-50,
[data-theme="modern-black"] small.text-muted,
[data-theme="modern-black"] .small {
    color: #e0e0e0 !important;
}

/* تحسين مظهر الحقول النشطة */
[data-theme="modern-black"] input:focus,
[data-theme="modern-black"] select:focus,
[data-theme="modern-black"] textarea:focus,
[data-theme="modern-black"] .form-control:focus {
    border-color: #00D2FF !important;
    box-shadow: 0 0 0 0.25rem rgba(0, 210, 255, 0.25) !important;
    background-color: rgba(0, 210, 255, 0.05) !important;
}

/* تجاوز أنماط النصوص المخفية والصفحات */
[data-theme="modern-black"] .page-item .page-link,
[data-theme="modern-black"] .pagination .page-link {
    color: #ffffff !important;
    background-color: #1a1a1a !important;
    border-color: #333 !important;
}

/* تحسين وضوح النصوص الداكنة والتأكد من أنها بيضاء */
[data-theme="modern-black"] .text-dark,
[data-theme="modern-black"] .text-muted,
[data-theme="modern-black"] .text-secondary,
[data-theme="modern-black"] .text-body,
[data-theme="modern-black"] h1, 
[data-theme="modern-black"] h2, 
[data-theme="modern-black"] h3, 
[data-theme="modern-black"] h4, 
[data-theme="modern-black"] h5, 
[data-theme="modern-black"] h6,
[data-theme="modern-black"] .form-label,
[data-theme="modern-black"] .form-check-label,
[data-theme="modern-black"] .form-text,
[data-theme="modern-black"] p,
[data-theme="modern-black"] span,
[data-theme="modern-black"] div,
[data-theme="modern-black"] td,
[data-theme="modern-black"] th,
[data-theme="modern-black"] li,
[data-theme="modern-black"] a:not(.btn):not(.nav-link),
[data-theme="modern-black"] label,
[data-theme="modern-black"] .dropdown-item,
[data-theme="modern-black"] .alert {
    color: var(--text-primary) !important;
    text-shadow: none !important;
}

/* تحسين القوائم المنسدلة */
[data-theme="modern-black"] .dropdown-menu {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--primary) !important;
    box-shadow: 0 0 15px rgba(0, 210, 255, 0.3) !important;
}

[data-theme="modern-black"] .dropdown-item {
    color: var(--text-primary) !important;
    background-color: transparent !important;
}

[data-theme="modern-black"] .dropdown-item:hover {
    background-color: var(--primary) !important;
    color: #000000 !important;
}

/* Modern Black Theme Fixes */

/* تحسين العناصر الرئيسية */
[data-theme="modern-black"] {
    --background: #0D1117;
    --elevated-bg: #161B22;
    --card-bg: #21262D;
    --border: #30363D;
    --primary: #00D2FF;
    --secondary: #00FF88;
    --accent: #00FF88;
    --success: #00FF88;
    --error: #FF5252;
    --warning: #FFBC00;
    --info: #59C2FF;
    --text-primary: #F0F6FC;
    --text-secondary: #8B949E;
    --shadow: 0 4px 15px rgba(0,0,0,0.2);
    --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
    --radius: 10px;
    --radius-large: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين الخلفية والنص */
[data-theme="modern-black"] body {
    background-color: var(--background);
    color: var(--text-primary);
}

/* تحسين البطاقات */
[data-theme="modern-black"] .card {
    background-color: var(--card-bg);
    border-color: var(--border);
    color: var(--text-primary);
    box-shadow: var(--shadow);
}

/* تحسين الجداول */
[data-theme="modern-black"] .table {
    background-color: transparent;
    color: var(--text-primary);
}

[data-theme="modern-black"] .table th {
    background-color: var(--elevated-bg);
    color: var(--text-primary);
    border-color: var(--border);
}

[data-theme="modern-black"] .table td {
    color: var(--text-primary);
    border-color: var(--border);
}

[data-theme="modern-black"] .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(33, 38, 45, 0.6);
}

[data-theme="modern-black"] .table-hover tbody tr:hover {
    background-color: var(--elevated-bg);
    color: var(--text-primary);
}

/* تحسين النماذج */
[data-theme="modern-black"] .form-control {
    background-color: var(--elevated-bg);
    border-color: var(--border);
    color: var(--text-primary);
}

[data-theme="modern-black"] .form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 210, 255, 0.25);
    background-color: var(--elevated-bg);
    color: var(--text-primary);
}

/* تحسين مسار التنقل */
[data-theme="modern-black"] nav[aria-label="breadcrumb"] {
    background-color: var(--card-bg);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    color: var(--text-primary);
    padding: 0.75rem 1rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-light);
}

[data-theme="modern-black"] nav[aria-label="breadcrumb"] *,
[data-theme="modern-black"] .page-header .breadcrumb {
    color: #ffffff;
    text-shadow: 0 0 3px rgba(0, 210, 255, 0.5);
    font-weight: 500;
}

/* تحسين الرابط النشط في مخطط الصفحة */
[data-theme="modern-black"] .breadcrumb-item.active {
    color: #00D2FF;
    font-weight: bold;
}

[data-theme="modern-black"] .breadcrumb-item+.breadcrumb-item::before {
    color: #00D2FF;
}

/* تحسين النافذة المنبثقة */
[data-theme="modern-black"] .modal-content {
    background-color: var(--card-bg);
    border: 1px solid var(--primary);
}

[data-theme="modern-black"] .modal-header,
[data-theme="modern-black"] .modal-footer {
    border-color: var(--primary);
}

/* تحسين الأزرار في الثيم */
[data-theme="modern-black"] .btn-primary {
    background-color: var(--primary);
    color: #000;
    border-color: var(--primary);
}

[data-theme="modern-black"] .btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
}

[data-theme="modern-black"] .btn-outline-primary:hover {
    background-color: var(--primary);
    color: #000;
}

/* تحسين الهوامش في الثيم */
[data-theme="modern-black"] .alert {
    border-radius: var(--radius);
    border: none;
    box-shadow: var(--shadow-light);
}

[data-theme="modern-black"] .alert-primary {
    background-color: rgba(0, 210, 255, 0.1);
    color: var(--primary);
    border-left: 4px solid var(--primary);
}

[data-theme="modern-black"] .alert-success {
    background-color: rgba(0, 255, 136, 0.1);
    color: var(--success);
    border-left: 4px solid var(--success);
}

[data-theme="modern-black"] .alert-danger {
    background-color: rgba(255, 82, 82, 0.1);
    color: var(--error);
    border-left: 4px solid var(--error);
}

[data-theme="modern-black"] .alert-warning {
    background-color: rgba(255, 188, 0, 0.1);
    color: var(--warning);
    border-left: 4px solid var(--warning);
}

[data-theme="modern-black"] #themeSelector option {
    background-color: #1a1a1a;
    color: var(--text-primary);
    padding: 10px;
}

[data-theme="modern-black"] #themeSelector option:hover {
    background-color: var(--primary);
    color: #000;
}

/* إضافة إطار متوهج عند تحديد الثيم */
[data-theme="modern-black"] #themeSelector:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(0, 210, 255, 0.5);
}
