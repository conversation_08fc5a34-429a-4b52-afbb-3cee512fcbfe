/**
 * أنماط بطاقات الإحصائيات الموحدة للمخزون
 */

/* بطاقات الإحصاءات الموحدة */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-left: 4px solid #4e73df; /* لون موحد لجميع البطاقات */
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* أيقونة داخل البطاقة */
.stat-card-icon {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 3rem;
    opacity: 0.1;
    color: #4e73df; /* لون موحد لجميع الأيقونات */
}

.stat-card-content {
    position: relative;
    z-index: 1;
    padding-right: 10px;
}

.stat-card-title {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: #343a40;
}

.stat-card-change {
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-card-change.positive {
    color: #1cc88a;
}

.stat-card-change.negative {
    color: #e74a3b;
}

/* مقاييس المخزون في الأعلى */
.inventory-metrics {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 20px;
}

.inventory-metrics-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #343a40;
}

/* تكييف للأجهزة المحمولة */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
    }
}
