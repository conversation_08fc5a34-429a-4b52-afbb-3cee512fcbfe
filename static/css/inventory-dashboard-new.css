/**
 * أنماط لوحة تحكم المخزون الجديدة
 */

/* ===== التخطيط الأساسي ===== */
.inventory-dashboard-container {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 56px);
    background-color: #f8f9fa;
}

/* ===== شريط العنوان ===== */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-title h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    color: #343a40;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.search-box .input-group {
    width: 250px;
}

.notifications .btn,
.user-menu .btn {
    color: #6c757d;
    padding: 0.375rem 0.75rem;
    position: relative;
}

.notifications .badge {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 0.65rem;
    padding: 0.2rem 0.4rem;
}

.user-menu .btn span {
    margin-right: 5px;
}

.notification-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
}

.notification-icon {
    margin-left: 10px;
    font-size: 1.2rem;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 0.85rem;
    font-weight: 500;
}

.notification-time {
    font-size: 0.75rem;
    color: #6c757d;
}

/* ===== شريط التنقل ===== */
.dashboard-breadcrumb {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #fff;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb {
    margin-bottom: 0;
}

.quick-actions {
    display: flex;
    gap: 10px;
}

/* ===== محتوى الصفحة ===== */
.dashboard-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* ===== شريط التذييل ===== */
.dashboard-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #fff;
    border-top: 1px solid #e9ecef;
    font-size: 0.85rem;
    color: #6c757d;
}

.footer-links {
    display: flex;
    gap: 15px;
}

.footer-links a {
    color: #6c757d;
    text-decoration: none;
}

.footer-links a:hover {
    color: #007bff;
    text-decoration: underline;
}

/* ===== بطاقات الأيقونات ===== */
.icon-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.icon-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #343a40;
    height: 180px;
}

.icon-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: #343a40;
}

.icon-card-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.icon-card:hover .icon-card-icon {
    background-color: #007bff;
    color: #fff;
}

.icon-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.icon-card-subtitle {
    font-size: 0.85rem;
    color: #6c757d;
}

/* ===== البطاقات الإحصائية ===== */
.stats-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-card-icon {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 3rem;
    opacity: 0.1;
    color: #007bff;
}

.stat-card-content {
    position: relative;
    z-index: 1;
}

.stat-card-title {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: #343a40;
}

.stat-card-change {
    font-size: 0.85rem;
    display: flex;
    align-items: center;
}

.stat-card-change.positive {
    color: #28a745;
}

.stat-card-change.negative {
    color: #dc3545;
}

.stat-card-change i {
    margin-left: 5px;
}

/* ===== الجداول ===== */
.data-table-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    overflow: hidden;
}

.data-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.data-table-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.data-table-actions {
    display: flex;
    gap: 10px;
}

.data-table-body {
    padding: 0;
}

.data-table-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* ===== الرسوم البيانية ===== */
.chart-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    overflow: hidden;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.chart-body {
    padding: 20px;
    height: 300px;
}

/* ===== دعم الثيم الأسود الحديث ===== */
[data-theme="modern-black"] .inventory-dashboard-container {
    background-color: var(--background) !important;
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .dashboard-header {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border) !important;
    box-shadow: var(--shadow-light) !important;
}

[data-theme="modern-black"] .header-title h1 {
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .notifications .btn,
[data-theme="modern-black"] .user-menu .btn {
    color: var(--text-secondary) !important;
    background: var(--elevated-bg) !important;
    border: 1px solid var(--border) !important;
}

[data-theme="modern-black"] .notifications .btn:hover,
[data-theme="modern-black"] .user-menu .btn:hover {
    background: var(--primary) !important;
    color: #000000 !important;
}

[data-theme="modern-black"] .dashboard-breadcrumb {
    background-color: var(--card-bg) !important;
    border-color: var(--border) !important;
}

[data-theme="modern-black"] .breadcrumb-item a {
    color: var(--primary) !important;
}

[data-theme="modern-black"] .breadcrumb-item.active {
    color: var(--text-secondary) !important;
}

[data-theme="modern-black"] .dashboard-content {
    background-color: var(--background) !important;
}

[data-theme="modern-black"] .icon-card {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
    color: var(--text-primary) !important;
    box-shadow: var(--shadow-light) !important;
}

[data-theme="modern-black"] .icon-card:hover {
    background-color: var(--elevated-bg) !important;
    transform: translateY(-8px) !important;
    box-shadow: var(--shadow) !important;
}

[data-theme="modern-black"] .icon-card-icon {
    background-color: var(--elevated-bg) !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border) !important;
}

[data-theme="modern-black"] .icon-card:hover .icon-card-icon {
    background-color: var(--primary) !important;
    color: #000000 !important;
    border-color: var(--primary) !important;
}

[data-theme="modern-black"] .icon-card-title {
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .icon-card-subtitle {
    color: var(--text-tertiary) !important;
}

[data-theme="modern-black"] .stat-card {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
    box-shadow: var(--shadow-light) !important;
}

[data-theme="modern-black"] .stat-card:hover {
    background-color: var(--elevated-bg) !important;
    transform: translateY(-8px) !important;
    box-shadow: var(--shadow) !important;
}

[data-theme="modern-black"] .stat-card-icon {
    color: var(--primary) !important;
    opacity: 0.15 !important;
}

[data-theme="modern-black"] .stat-card-title {
    color: var(--text-secondary) !important;
}

[data-theme="modern-black"] .stat-card-value {
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .stat-card-change.positive {
    color: var(--success) !important;
}

[data-theme="modern-black"] .stat-card-change.negative {
    color: var(--error) !important;
}

[data-theme="modern-black"] .data-table-container {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
    box-shadow: var(--shadow-light) !important;
}

[data-theme="modern-black"] .data-table-header {
    background-color: var(--elevated-bg) !important;
    border-bottom: 1px solid var(--border) !important;
}

[data-theme="modern-black"] .data-table-title {
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .data-table-footer {
    background-color: var(--card-bg) !important;
    border-top: 1px solid var(--border) !important;
}

[data-theme="modern-black"] .chart-container {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
    box-shadow: var(--shadow-light) !important;
}

[data-theme="modern-black"] .chart-header {
    background-color: var(--elevated-bg) !important;
    border-bottom: 1px solid var(--border) !important;
}

[data-theme="modern-black"] .chart-title {
    color: var(--text-primary) !important;
}

[data-theme="modern-black"] .chart-body {
    background-color: var(--card-bg) !important;
}

[data-theme="modern-black"] .dashboard-footer {
    background-color: var(--card-bg) !important;
    border-top: 1px solid var(--border) !important;
    color: var(--text-secondary) !important;
}

[data-theme="modern-black"] .footer-links a {
    color: var(--text-secondary) !important;
}

[data-theme="modern-black"] .footer-links a:hover {
    color: var(--primary) !important;
}

/* أيقونات ملونة خاصة بالمخزون */
[data-theme="modern-black"] .icon-card[href*="products"] .icon-card-icon,
[data-theme="modern-black"] .icon-card[href*="منتجات"] .icon-card-icon {
    background: linear-gradient(135deg, #B537F2, #9C27B0) !important;
}

[data-theme="modern-black"] .icon-card[href*="categories"] .icon-card-icon,
[data-theme="modern-black"] .icon-card[href*="فئات"] .icon-card-icon {
    background: linear-gradient(135deg, #00FF88, #00CC6A) !important;
}

[data-theme="modern-black"] .icon-card[href*="suppliers"] .icon-card-icon,
[data-theme="modern-black"] .icon-card[href*="موردين"] .icon-card-icon {
    background: linear-gradient(135deg, #FF6B00, #E6004D) !important;
}

[data-theme="modern-black"] .icon-card[href*="warehouses"] .icon-card-icon,
[data-theme="modern-black"] .icon-card[href*="مخازن"] .icon-card-icon {
    background: linear-gradient(135deg, #00F5D4, #00D2FF) !important;
}

[data-theme="modern-black"] .icon-card[href*="orders"] .icon-card-icon,
[data-theme="modern-black"] .icon-card[href*="طلبات"] .icon-card-icon {
    background: linear-gradient(135deg, #FFD700, #E6C200) !important;
}

[data-theme="modern-black"] .icon-card[href*="reports"] .icon-card-icon,
[data-theme="modern-black"] .icon-card[href*="تقارير"] .icon-card-icon {
    background: linear-gradient(135deg, #3A86FF, #0066CC) !important;
}

/* ===== الاستجابة للشاشات المختلفة ===== */
@media (max-width: 992px) {
    .icon-cards-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .stats-cards-container {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .header-title {
        margin-bottom: 15px;
    }
    
    .header-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .search-box .input-group {
        width: 100%;
    }
    
    .dashboard-breadcrumb {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .breadcrumb {
        margin-bottom: 10px;
    }
    
    .dashboard-footer {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-info {
        margin-bottom: 10px;
    }
    
    .footer-links {
        justify-content: center;
    }
    
    /* تحسينات للثيم الأسود في الشاشات الصغيرة */
    [data-theme="modern-black"] .icon-card {
        padding: 15px !important;
    }
    
    [data-theme="modern-black"] .icon-card-icon {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.3rem !important;
    }
    
    [data-theme="modern-black"] .stat-card {
        padding: 15px !important;
    }
}
