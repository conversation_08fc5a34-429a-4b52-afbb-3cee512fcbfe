{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تصدير قاعدة بيانات" %}{% endblock %}

{% block extra_css %}
<style>
    .export-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 30px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }
    .export-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    .export-section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }
    .export-section h4 {
        margin-bottom: 20px;
        color: #007bff;
    }
    .export-footer {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        text-align: center;
    }
    .format-options {
        display: flex;
        gap: 20px;
        margin-top: 20px;
    }
    .format-option {
        flex: 1;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }
    .format-option:hover {
        border-color: #007bff;
    }
    .format-option.active {
        border-color: #007bff;
        background-color: #f0f7ff;
    }
    .format-option img {
        height: 50px;
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
    <div class="export-container">
        <div class="export-header">
            <h1>{% trans "تصدير قاعدة بيانات" %}</h1>
            <p class="lead">{% trans "قم بتصدير البيانات من قاعدة البيانات إلى ملف" %}</p>
            {% if default_db %}
                <div class="alert alert-info">
                    <i class="fas fa-database"></i>
                    {% trans "قاعدة البيانات الحالية:" %} <strong>{{ default_db.name }}</strong>
                    <span class="badge badge-primary">PostgreSQL</span>
                </div>
            {% endif %}
        </div>

        <form method="post">
            {% csrf_token %}

            <div class="export-section">
                <h4>{% trans "اختر قاعدة البيانات" %}</h4>

                <div class="form-group">
                    <label for="database_config">{% trans "قاعدة البيانات" %}</label>
                    {% if default_db %}
                        <div class="alert alert-primary mb-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-database me-2"></i>
                                <div>
                                    <strong>{% trans "قاعدة البيانات الحالية:" %} {{ default_db.name }}</strong>
                                    <div><small>{% trans "تم تحديد قاعدة البيانات الافتراضية تلقائيًا" %}</small></div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    <select name="database_config" id="database_config" class="form-control">
                        {% for db in databases %}
                            <option value="{{ db.id }}" {% if db.is_default %}selected{% endif %}>{{ db.name }} ({{ db.get_db_type_display }})</option>
                        {% endfor %}
                    </select>
                    <small class="form-text text-muted">{% trans "اختر قاعدة البيانات التي تريد تصدير البيانات منها" %}</small>
                </div>
            </div>

            <div class="export-section">
                <h4>{% trans "اختر تنسيق التصدير" %}</h4>

                <div class="format-options">
                    <div class="format-option active" data-format="json">
                        <i class="fas fa-file-code fa-3x mb-2 text-primary"></i>
                        <h5>JSON</h5>
                        <p>{% trans "تنسيق قياسي لتبادل البيانات" %}</p>
                        <input type="radio" name="format" value="json" checked style="display: none;">
                    </div>
                    <div class="format-option" data-format="dump">
                        <i class="fas fa-database fa-3x mb-2 text-success"></i>
                        <h5>SQL Dump</h5>
                        <p>{% trans "نسخة احتياطية كاملة لقاعدة البيانات" %}</p>
                        <input type="radio" name="format" value="dump" style="display: none;">
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i> {% trans "ملاحظة: تنسيق SQL Dump متاح فقط لقواعد بيانات PostgreSQL." %}
                </div>
            </div>

            <div class="export-footer">
                <button type="submit" class="btn btn-primary btn-lg">{% trans "تصدير" %}</button>
                <a href="{% url 'db_manager:database_list' %}" class="btn btn-secondary btn-lg">{% trans "إلغاء" %}</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تحديد تنسيق التصدير
        $('.format-option').click(function() {
            $('.format-option').removeClass('active');
            $(this).addClass('active');

            var format = $(this).data('format');
            $('input[name="format"][value="' + format + '"]').prop('checked', true);
        });

        // تحديث خيارات التنسيق بناءً على نوع قاعدة البيانات
        $('#database_config').change(function() {
            var selectedOption = $(this).find('option:selected');
            var dbType = selectedOption.text().includes('PostgreSQL') ? 'postgresql' : 'other';

            if (dbType !== 'postgresql') {
                // إذا لم تكن قاعدة البيانات من نوع PostgreSQL، قم بتعطيل خيار SQL Dump
                $('.format-option[data-format="dump"]').addClass('disabled').css('opacity', '0.5');
                // وتأكد من تحديد JSON
                $('.format-option[data-format="json"]').click();
            } else {
                // إذا كانت قاعدة البيانات من نوع PostgreSQL، قم بتمكين خيار SQL Dump
                $('.format-option[data-format="dump"]').removeClass('disabled').css('opacity', '1');
            }
        }).trigger('change');
    });
</script>
{% endblock %}
