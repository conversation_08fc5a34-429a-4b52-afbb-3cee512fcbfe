{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{% trans "إنشاء رمز إعداد جديد" %}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 30px;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }
    .form-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
        text-align: center;
    }
    .form-header i {
        font-size: 48px;
        color: #007bff;
        margin-bottom: 20px;
    }
    .form-content {
        margin-bottom: 30px;
    }
    .form-footer {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="form-container">
        <div class="form-header">
            <i class="fas fa-key"></i>
            <h1>{% trans "إنشاء رمز إعداد جديد" %}</h1>
            <p class="lead">{% trans "قم بإنشاء رمز إعداد جديد للسماح بإعداد النظام" %}</p>
        </div>
        
        <div class="form-content">
            <div class="alert alert-info">
                <p>{% trans "رموز الإعداد تسمح للمستخدمين بإعداد النظام لأول مرة. يمكن استخدام كل رمز مرة واحدة فقط وينتهي بعد فترة محددة." %}</p>
            </div>
            
            <form method="post">
                {% csrf_token %}
                
                <div class="form-group">
                    <label for="{{ form.expiry_hours.id_for_label }}">{% trans "مدة صلاحية الرمز (بالساعات)" %}</label>
                    {{ form.expiry_hours|add_class:"form-control" }}
                    <small class="form-text text-muted">{{ form.expiry_hours.help_text }}</small>
                    {% if form.expiry_hours.errors %}
                        <div class="invalid-feedback d-block">{{ form.expiry_hours.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="form-footer">
                    <button type="submit" class="btn btn-primary btn-lg">{% trans "إنشاء رمز" %}</button>
                    <a href="{% url 'db_manager:token_list' %}" class="btn btn-secondary btn-lg">{% trans "إلغاء" %}</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
