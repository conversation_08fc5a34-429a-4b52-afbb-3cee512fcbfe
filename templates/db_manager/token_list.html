{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "رموز الإعداد" %}{% endblock %}

{% block extra_css %}
<style>
    .token-card {
        margin-bottom: 20px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .token-card:hover {
        transform: translateY(-5px);
    }
    .token-card-header {
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
    }
    .token-card-body {
        padding: 20px;
    }
    .token-card-footer {
        padding: 15px;
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
    }
    .token-status {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 12px;
        margin-left: 10px;
    }
    .token-status.valid {
        background-color: #c3e6cb;
        color: #155724;
    }
    .token-status.used {
        background-color: #f5c6cb;
        color: #721c24;
    }
    .token-status.expired {
        background-color: #ffeeba;
        color: #856404;
    }
    .token-actions {
        display: flex;
        gap: 10px;
    }
    .token-empty {
        text-align: center;
        padding: 50px 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
        margin-top: 30px;
    }
    .token-empty i {
        font-size: 48px;
        color: #adb5bd;
        margin-bottom: 20px;
    }
    .token-value {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        font-family: monospace;
        word-break: break-all;
    }
    .setup-url {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        word-break: break-all;
        margin-top: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{% trans "رموز الإعداد" %}</h1>
        <a href="{% url 'db_manager:token_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {% trans "إنشاء رمز إعداد جديد" %}
        </a>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            {% if tokens %}
                <div class="row">
                    {% for token in tokens %}
                        <div class="col-md-6">
                            <div class="token-card">
                                <div class="token-card-header d-flex justify-content-between align-items-center">
                                    <h5>
                                        {% trans "رمز إعداد" %}
                                        {% if token.is_valid %}
                                            <span class="token-status valid">{% trans "صالح" %}</span>
                                        {% elif token.is_used %}
                                            <span class="token-status used">{% trans "مستخدم" %}</span>
                                        {% elif token.is_expired %}
                                            <span class="token-status expired">{% trans "منتهي" %}</span>
                                        {% endif %}
                                    </h5>
                                </div>
                                <div class="token-card-body">
                                    <p><strong>{% trans "الرمز:" %}</strong></p>
                                    <div class="token-value">{{ token.token }}</div>
                                    
                                    {% if token.is_valid %}
                                        <p class="mt-3"><strong>{% trans "رابط الإعداد:" %}</strong></p>
                                        <div class="setup-url">
                                            <a href="{% url 'db_manager:setup_with_token' token.token %}" target="_blank">
                                                {% url 'db_manager:setup_with_token' token.token %}
                                            </a>
                                        </div>
                                    {% endif %}
                                    
                                    <p class="mt-3"><strong>{% trans "تاريخ الإنشاء:" %}</strong> {{ token.created_at|date:"Y-m-d H:i" }}</p>
                                    <p><strong>{% trans "تاريخ انتهاء الصلاحية:" %}</strong> {{ token.expires_at|date:"Y-m-d H:i" }}</p>
                                    
                                    {% if token.is_used %}
                                        <p><strong>{% trans "تاريخ الاستخدام:" %}</strong> {{ token.used_at|date:"Y-m-d H:i" }}</p>
                                    {% endif %}
                                </div>
                                <div class="token-card-footer">
                                    <div class="token-actions">
                                        {% if token.is_valid %}
                                            <button class="btn btn-sm btn-outline-primary copy-url" data-url="{% url 'db_manager:setup_with_token' token.token %}">
                                                <i class="fas fa-copy"></i> {% trans "نسخ الرابط" %}
                                            </button>
                                        {% endif %}
                                        <a href="{% url 'db_manager:token_delete' token.id %}" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i> {% trans "حذف" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="token-empty">
                    <i class="fas fa-key"></i>
                    <h3>{% trans "لا توجد رموز إعداد" %}</h3>
                    <p>{% trans "لم يتم إنشاء أي رموز إعداد بعد. انقر على زر 'إنشاء رمز إعداد جديد' لإنشاء واحد." %}</p>
                    <a href="{% url 'db_manager:token_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "إنشاء رمز إعداد جديد" %}
                    </a>
                </div>
            {% endif %}
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>{% trans "معلومات" %}</h5>
                </div>
                <div class="card-body">
                    <p>{% trans "رموز الإعداد تسمح للمستخدمين بإعداد النظام لأول مرة. يمكن استخدام كل رمز مرة واحدة فقط وينتهي بعد فترة محددة." %}</p>
                    
                    <h6 class="mt-4">{% trans "إحصائيات" %}</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "إجمالي الرموز" %}
                            <span class="badge badge-primary badge-pill">{{ tokens|length }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "الرموز الصالحة" %}
                            <span class="badge badge-success badge-pill">{{ valid_tokens_count }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "الرموز المستخدمة" %}
                            <span class="badge badge-danger badge-pill">{{ used_tokens_count }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {% trans "الرموز المنتهية" %}
                            <span class="badge badge-warning badge-pill">{{ expired_tokens_count }}</span>
                        </li>
                    </ul>
                    
                    <div class="mt-4">
                        <a href="{% url 'db_manager:database_list' %}" class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-database"></i> {% trans "إدارة قواعد البيانات" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // نسخ رابط الإعداد
        $('.copy-url').click(function() {
            var url = $(this).data('url');
            var fullUrl = window.location.origin + url;
            
            // إنشاء عنصر نصي مؤقت
            var tempInput = $('<input>');
            $('body').append(tempInput);
            tempInput.val(fullUrl).select();
            
            // نسخ النص
            document.execCommand('copy');
            
            // إزالة العنصر المؤقت
            tempInput.remove();
            
            // تغيير نص الزر مؤقتًا
            var originalText = $(this).html();
            $(this).html('<i class="fas fa-check"></i> {% trans "تم النسخ" %}');
            
            // إعادة النص الأصلي بعد ثانيتين
            var button = $(this);
            setTimeout(function() {
                button.html(originalText);
            }, 2000);
        });
    });
</script>
{% endblock %}
