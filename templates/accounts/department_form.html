{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
    {% if department.id %}تعديل قسم - {{ department.name }}{% else %}إضافة قسم جديد{% endif %} - نظام الخواجه
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h4 class="mb-0">
                        {% if department.id %}
                            <i class="fas fa-edit"></i> تعديل قسم - {{ department.name }}
                        {% else %}
                            <i class="fas fa-plus-circle"></i> إضافة قسم جديد
                        {% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.name|as_crispy_field }}
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.code|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.order|as_crispy_field }}
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.is_active|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description|as_crispy_field }}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accounts:department_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right ml-1"></i> العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save ml-1"></i> حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}