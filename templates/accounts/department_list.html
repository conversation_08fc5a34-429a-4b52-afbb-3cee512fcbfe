{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الأقسام - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-sitemap"></i> قائمة الأقسام</h2>
        <a href="{% url 'accounts:department_create' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle"></i> إضافة قسم جديد
        </a>
    </div>

    <!-- البحث والتصفية -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="بحث بالاسم..." value="{{ search_query }}">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <select name="is_active" class="form-select" onchange="this.form.submit()">
                        <option value="">-- الحالة --</option>
                        <option value="true" {% if is_active == True %}selected{% endif %}>نشط</option>
                        <option value="false" {% if is_active == False %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-outline-primary w-100">تصفية</button>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الأقسام -->
    <div class="card">
        <div class="card-header bg-transparent">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">قائمة الأقسام ({{ total_departments }})</h5>
                </div>
                <div class="col-auto">
                    <a href="{% url 'accounts:department_order' %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-sort"></i> تغيير ترتيب الأقسام
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">الاسم</th>
                            <th scope="col">رمز القسم</th>
                            <th scope="col">الترتيب</th>
                            <th scope="col">الحالة</th>
                            <th scope="col" class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for department in page_obj %}
                        <tr id="department-row-{{ department.id }}">
                            <td>{{ forloop.counter }}</td>
                            <td>{{ department.name }}</td>
                            <td>{{ department.code|default:"-" }}</td>
                            <td>{{ department.order }}</td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input toggle-status" type="checkbox" id="status-{{ department.id }}" 
                                           data-id="{{ department.id }}" {% if department.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="status-{{ department.id }}">
                                        <span class="status-text-{{ department.id }}">
                                            {% if department.is_active %}نشط{% else %}غير نشط{% endif %}
                                        </span>
                                    </label>
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="btn-group">
                                    <a href="{% url 'accounts:department_update' department.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="{% url 'accounts:department_delete' department.id %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash-alt"></i> حذف
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-info-circle me-2"></i> لا يوجد أقسام مطابقة لمعايير البحث
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-transparent">
            {% include 'includes/pagination.html' with page_obj=page_obj %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تفعيل/تعطيل القسم
        $('.toggle-status').on('change', function() {
            const departmentId = $(this).data('id');
            const csrfToken = '{{ csrf_token }}';
            
            $.ajax({
                url: `/accounts/departments/${departmentId}/toggle/`,
                type: 'POST',
                headers: { 'X-CSRFToken': csrfToken },
                success: function(response) {
                    if (response.success) {
                        const status = response.is_active ? 'نشط' : 'غير نشط';
                        $(`.status-text-${departmentId}`).text(status);
                    } else {
                        // إذا كان هناك خطأ، قم بإعادة المفتاح إلى وضعه السابق
                        $('#status-' + departmentId).prop('checked', !$('#status-' + departmentId).prop('checked'));
                        alert('حدث خطأ أثناء تحديث حالة القسم.');
                    }
                },
                error: function() {
                    // إذا كان هناك خطأ، قم بإعادة المفتاح إلى وضعه السابق
                    $('#status-' + departmentId).prop('checked', !$('#status-' + departmentId).prop('checked'));
                    alert('حدث خطأ أثناء تحديث حالة القسم.');
                }
            });
        });
    });
</script>
{% endblock %}