{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if salesperson %}تعديل بيانات بائع{% else %}إضافة بائع جديد{% endif %} - نظام الخواجه
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-transparent">
                    <h3 class="mb-0">
                        {% if salesperson %}
                            <i class="fas fa-edit"></i> تعديل بيانات بائع: {{ salesperson.name }}
                        {% else %}
                            <i class="fas fa-user-plus"></i> إضافة بائع جديد
                        {% endif %}
                    </h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                يرجى تصحيح الأخطاء التالية:
                                {{ form.errors }}
                            </div>
                        {% endif %}

                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="{{ form.name.id_for_label }}" class="form-label">اسم البائع <span class="text-danger">*</span></label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">{{ form.name.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6">
                                <label for="{{ form.employee_number.id_for_label }}" class="form-label">الرقم الوظيفي</label>
                                {{ form.employee_number }}
                                {% if form.employee_number.errors %}
                                    <div class="invalid-feedback d-block">{{ form.employee_number.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6">
                                <label for="{{ form.branch.id_for_label }}" class="form-label">الفرع <span class="text-danger">*</span></label>
                                {{ form.branch }}
                                {% if form.branch.errors %}
                                    <div class="invalid-feedback d-block">{{ form.branch.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">رقم الهاتف</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="invalid-feedback d-block">{{ form.phone.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6">
                                <label for="{{ form.email.id_for_label }}" class="form-label">البريد الإلكتروني</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="invalid-feedback d-block">{{ form.email.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 d-flex align-items-center">
                                <div class="form-check form-switch mt-4">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        نشط
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <label for="{{ form.address.id_for_label }}" class="form-label">العنوان</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="invalid-feedback d-block">{{ form.address.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-12">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="invalid-feedback d-block">{{ form.notes.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'accounts:salesperson_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right ml-1"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save ml-1"></i> 
                                {% if salesperson %}حفظ التغييرات{% else %}إضافة بائع{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}