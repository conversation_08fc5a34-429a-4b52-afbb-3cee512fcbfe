{% extends 'base.html' %}
{% load static %}

{% block title %}معلومات الشركة - نظام الخواجه{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
            <li class="breadcrumb-item active" aria-current="page">معلومات الشركة</li>
        </ol>
    </nav>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">معلومات الشركة</h4>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="row mb-4">
                    <div class="col-md-12 text-center mb-4">
                        {% if company.logo %}
                            <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-fluid mb-3" style="max-height: 150px;">
                        {% else %}
                            <div class="alert alert-info">
                                لم يتم تحميل شعار الشركة بعد
                            </div>
                        {% endif %}
                        
                        <div class="mb-3">
                            <label for="{{ form.logo.id_for_label }}" class="form-label">شعار الشركة</label>
                            {{ form.logo }}
                            {% if form.logo.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.logo.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">معلومات أساسية</h5>
                        
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">اسم الشركة</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">وصف الشركة</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">العنوان</label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">رقم الهاتف</label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">البريد الإلكتروني</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.website.id_for_label }}" class="form-label">الموقع الإلكتروني</label>
                            {{ form.website }}
                            {% if form.website.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.website.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">معلومات قانونية</h5>
                        
                        <div class="mb-3">
                            <label for="{{ form.tax_number.id_for_label }}" class="form-label">الرقم الضريبي</label>
                            {{ form.tax_number }}
                            {% if form.tax_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.tax_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.commercial_register.id_for_label }}" class="form-label">السجل التجاري</label>
                            {{ form.commercial_register }}
                            {% if form.commercial_register.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.commercial_register.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <h5 class="border-bottom pb-2 mb-3 mt-4">وسائل التواصل الاجتماعي</h5>
                        
                        <div class="mb-3">
                            <label for="{{ form.facebook.id_for_label }}" class="form-label">فيسبوك</label>
                            {{ form.facebook }}
                            {% if form.facebook.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.facebook.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.twitter.id_for_label }}" class="form-label">تويتر</label>
                            {{ form.twitter }}
                            {% if form.twitter.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.twitter.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.instagram.id_for_label }}" class="form-label">انستغرام</label>
                            {{ form.instagram }}
                            {% if form.instagram.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.instagram.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.linkedin.id_for_label }}" class="form-label">لينكد إن</label>
                            {{ form.linkedin }}
                            {% if form.linkedin.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.linkedin.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5 class="border-bottom pb-2 mb-3">معلومات إضافية</h5>
                        
                        <div class="mb-3">
                            <label for="{{ form.about.id_for_label }}" class="form-label">نبذة عن الشركة</label>
                            {{ form.about }}
                            {% if form.about.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.about.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.vision.id_for_label }}" class="form-label">رؤية الشركة</label>
                            {{ form.vision }}
                            {% if form.vision.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.vision.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.mission.id_for_label }}" class="form-label">رسالة الشركة</label>
                            {{ form.mission }}
                            {% if form.mission.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.mission.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5 class="border-bottom pb-2 mb-3">إعدادات النظام</h5>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.primary_color.id_for_label }}" class="form-label">اللون الرئيسي</label>
                                    <div class="input-group">
                                        {{ form.primary_color }}
                                        <span class="input-group-text">
                                            <div style="width: 20px; height: 20px; background-color: {{ company.primary_color|default:'#007bff' }}; border-radius: 3px;"></div>
                                        </span>
                                    </div>
                                    {% if form.primary_color.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.primary_color.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.secondary_color.id_for_label }}" class="form-label">اللون الثانوي</label>
                                    <div class="input-group">
                                        {{ form.secondary_color }}
                                        <span class="input-group-text">
                                            <div style="width: 20px; height: 20px; background-color: {{ company.secondary_color|default:'#6c757d' }}; border-radius: 3px;"></div>
                                        </span>
                                    </div>
                                    {% if form.secondary_color.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.secondary_color.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.accent_color.id_for_label }}" class="form-label">لون التمييز</label>
                                    <div class="input-group">
                                        {{ form.accent_color }}
                                        <span class="input-group-text">
                                            <div style="width: 20px; height: 20px; background-color: {{ company.accent_color|default:'#28a745' }}; border-radius: 3px;"></div>
                                        </span>
                                    </div>
                                    {% if form.accent_color.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.accent_color.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
