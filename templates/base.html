<!DOCTYPE html>
{% load static %}
{% load custom_filters %}
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة العملاء{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">

    <!-- Animate.css for SweetAlert animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    <!-- Modern Black Theme CSS -->
    <link rel="stylesheet" href="{% static 'css/modern-black-theme.css' %}">
    
    <!-- Modern Black Theme Fixes -->
    <link rel="stylesheet" href="{% static 'css/modern-black-fixes.css' %}">
      <!-- Extra Dark Mode Fixes -->
    <link rel="stylesheet" href="{% static 'css/extra-dark-fixes.css' %}">
    
    <!-- Custom Theme Enhancements -->
    <link rel="stylesheet" href="{% static 'css/custom-theme-enhancements.css' %}">

    <!-- Google Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <!-- Google Fonts - Roboto -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Database Card CSS -->
    {% if 'database' in request.path %}
    <link rel="stylesheet" href="{% static 'css/db_card.css' %}">
    {% endif %}    <!-- تطبيق فوري للثيم لتجنب الوميض -->
    <script>
        // تطبيق الثيم فوراً قبل رسم الصفحة - محسن لتجنب الوميض
        (function() {
            // إخفاء body مؤقتاً لتجنب الوميض
            document.documentElement.style.visibility = 'hidden';
            
            {% if user.is_authenticated %}
                var userDefaultTheme = '{{ user.default_theme }}';
                // Always prefer user's default theme from database after login
                if (userDefaultTheme && userDefaultTheme !== 'default') {
                    document.documentElement.setAttribute('data-theme', userDefaultTheme);
                    localStorage.setItem('selectedTheme', userDefaultTheme);
                    if (document.getElementById('themeSelector')) {
                        document.getElementById('themeSelector').value = userDefaultTheme;
                    }
                } else {
                    var savedTheme = localStorage.getItem('selectedTheme');
                    // Only use localStorage theme if no default theme is set
                    if (savedTheme && savedTheme !== 'default') {
                        document.documentElement.setAttribute('data-theme', savedTheme);
                    }
                }
            {% else %}
                // For non-authenticated users, use localStorage theme
                var savedTheme = localStorage.getItem('selectedTheme');
                if (savedTheme && savedTheme !== 'default') {
                    document.documentElement.setAttribute('data-theme', savedTheme);
                }
            {% endif %}
            
            // إظهار الصفحة بعد تطبيق الثيم
            setTimeout(function() {
                document.documentElement.style.visibility = 'visible';
            }, 10);
        })();
    </script>

    <!-- CSS فوري للثيم الافتراضي لتجنب الوميض -->
    <style>
        /* CSS فوري للثيم الافتراضي - الأيقونات فوق النصوص في navbar */
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            text-align: center !important;
            padding: 0.6rem 1rem !important;
            min-height: 65px !important;
            justify-content: center !important;
            line-height: 1.1 !important;
            gap: 0.3rem !important;
        }
        
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link i,
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link .fas,
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link .far,
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link .fab {
            font-size: 1.4rem !important;
            margin: 0 !important;
            display: block !important;
            order: 1 !important;
            width: 100% !important;
            text-align: center !important;
        }
        
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link .nav-text,
        html:not([data-theme="custom-theme"]):not([data-theme="modern-black"]) body .navbar .nav-link span {
            order: 2 !important;
            font-size: 0.8rem !important;
            display: block !important;
            width: 100% !important;
            text-align: center !important;
            margin: 0 !important;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body id="main-body" data-user-authenticated="{% if user.is_authenticated %}true{% else %}false{% endif %}">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <div class="d-flex align-items-center">
                    {% if company_info.logo %}
                        <img src="{{ company_info.logo.url }}" alt="{{ company_info.name|default:'شعار النظام' }}" class="logo-img">
                    {% else %}
                        <img src="{% static 'img/logo.png' %}" alt="شعار النظام" class="logo-img">
                    {% endif %}
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}"><i class="fas fa-home"></i><span class="nav-text">الرئيسية</span></a>
                    </li>

                    <!-- الأقسام للمستخدمين المسجلين فقط -->
                    {% if user.is_authenticated %}
                        {% if user.is_staff %}
                            <!-- الموظفون يرون جميع الأقسام -->                            <li class="nav-item">
                                <a class="nav-link" href="/customers/">
                                    <i class="fas fa-users"></i><span class="nav-text">العملاء</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/orders/">
                                    <i class="fas fa-shopping-cart"></i><span class="nav-text">الطلبات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/inventory/">
                                    <i class="fas fa-boxes"></i><span class="nav-text">المخزون</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/inspections/">
                                    <i class="fas fa-clipboard-check"></i><span class="nav-text">المعاينات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/installations/">
                                    <i class="fas fa-tools"></i><span class="nav-text">التركيبات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/factory/">
                                    <i class="fas fa-industry"></i><span class="nav-text">المصنع</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/reports/list/">
                                    <i class="fas fa-chart-bar"></i><span class="nav-text">التقارير</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/database/">
                                    <i class="fas fa-database"></i><span class="nav-text">إدارة البيانات</span>
                                </a>
                            </li>
                        {% else %}                            <!-- المستخدمون العاديون يرون الأقسام المخصصة لهم فقط -->
                            {% for dept in user_parent_departments %}
                                <li class="nav-item">
                                    <a class="nav-link" href="/{{ dept.code }}/" title="{{ dept.name }}">
                                        <i class="{{ dept.icon|default:'fas fa-folder' }}"></i><span class="nav-text">{{ dept.name }}</span>
                                    </a>
                                </li>
                            {% endfor %}
                        {% endif %}
                    {% else %}                        <!-- رسالة للمستخدمين غير المسجلين -->
                        <li class="nav-item">
                            <span class="nav-link text-muted">
                                <i class="fas fa-lock"></i><span class="nav-text">يرجى تسجيل الدخول للوصول للأقسام</span>
                            </span>
                        </li>
                    {% endif %}
                </ul>
                <div class="d-flex align-items-center ms-auto">
                    {% if user.is_authenticated %}
                    <!-- Notifications Dropdown -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-light position-relative" type="button" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            {% if notifications_count > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{ notifications_count }}
                                <span class="visually-hidden">إشعارات غير مقروءة</span>
                            </span>
                            {% endif %}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationsDropdown" style="min-width: 350px; max-height: 400px; overflow-y: auto;">
                            <li>
                                <h6 class="dropdown-header">الإشعارات</h6>
                            </li>
                            {% if recent_notifications %}
                                {% for notification in recent_notifications %}
                                <li>
                                    <a class="dropdown-item{% if not notification.is_read %} bg-light{% endif %} py-3" href="{% url 'accounts:notification_detail' notification.id %}" style="border-bottom: 1px solid #eee;">
                                        <div class="d-flex align-items-start">
                                            <div class="flex-shrink-0 me-3">
                                                {% if notification.priority == 'urgent' %}
                                                    <i class="fas fa-exclamation-triangle text-danger fa-lg"></i>
                                                {% elif notification.priority == 'high' %}
                                                    <i class="fas fa-exclamation-circle text-warning fa-lg"></i>
                                                {% else %}
                                                    <i class="fas fa-bell text-info fa-lg"></i>
                                                {% endif %}
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-start mb-1">
                                                    <div class="fw-bold text-truncate" style="max-width: 200px;">{{ notification.title }}</div>
                                                    {% if not notification.is_read %}
                                                        <span class="badge bg-primary rounded-pill ms-2">جديد</span>
                                                    {% endif %}
                                                </div>
                                                <div class="small text-muted mb-1">
                                                    <i class="fas fa-clock me-1"></i>
                                                    {{ notification.created_at|date:"Y-m-d H:i" }}
                                                </div>
                                                <div class="small text-muted">
                                                    <i class="fas fa-hashtag me-1"></i>
                                                    #{{ notification.id }}
                                                    <span class="ms-2">
                                                        {% if notification.priority == 'urgent' %}
                                                            <span class="badge bg-danger rounded-pill">عاجل</span>
                                                        {% elif notification.priority == 'high' %}
                                                            <span class="badge bg-warning text-dark rounded-pill">مهم</span>
                                                        {% else %}
                                                            <span class="badge bg-info rounded-pill">عادي</span>
                                                        {% endif %}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                {% endfor %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="{% url 'accounts:notifications' %}">عرض كل الإشعارات</a></li>
                            {% else %}
                                <li><div class="dropdown-item text-center">لا توجد إشعارات</div></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="{% url 'accounts:notifications' %}">عرض كل الإشعارات</a></li>
                            {% endif %}
                        </ul>
                    </div>

                    <!-- User Dropdown -->                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user"></i> {{ user.get_full_name|default:user.username }}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <div class="px-3 py-2">                                    <label for="themeSelector" class="form-label" style="font-weight: bold;">اختر الثيم المناسب</label>
                                    <select id="themeSelector" class="form-select">
                                        <option value="default">الثيم الإفتراضي</option>
                                        <option value="custom-theme">نسخة الثيم الإفتراضي القابلة للتعديل</option>
                                        <option value="modern-black">الثيم الأسود العصري</option>
                                    </select>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}"><i class="fas fa-id-card"></i> الملف الشخصي</a></li>
                            {% if user.is_staff %}
                            <li><a class="dropdown-item" href="{% url 'admin:index' %}"><i class="fas fa-cog"></i> لوحة الإدارة</a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                        </ul>
                    </div>
                    {% else %}
                    <a href="{% url 'accounts:login' %}" class="btn btn-outline-light"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>      <!-- Branch Messages - Only shown on home page -->
    {% if branch_messages and request.path == '/' %}
    <script>
        // Wait for all DOM content and resources to load for optimal display timing
        window.addEventListener('load', function() {
            const branchMessages = [
                {% for message in branch_messages %}
                    {
                        title: "{{ message.title|escapejs }}",
                        message: "{{ message.message|escapejs }}",
                        type: "{{ message.message_type|escapejs }}",
                        color: "{{ message.color|escapejs }}",
                        icon: "{{ message.icon|escapejs }}"
                    }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ];

            let currentMessageIndex = 0;
            const showMessage = () => {
                if (currentMessageIndex >= branchMessages.length) return;
                
                const message = branchMessages[currentMessageIndex];
                currentMessageIndex++;

                Swal.fire({
                    title: message.title,
                    html: message.message,
                    icon: getMessageIcon(message.type),
                    timer: 8000,
                    timerProgressBar: true,
                    showClass: {
                        popup: 'animate__animated animate__fadeInDown'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOutUp'
                    },
                    customClass: {
                        popup: 'rtl-popup',
                        title: 'rtl-title',
                        content: 'rtl-content',
                        container: 'branch-message-container'
                    },
                    showCloseButton: true,
                    position: 'top-start',
                    showConfirmButton: false,
                    allowOutsideClick: true,
                    background: getMessageBackground(message.color),
                    didOpen: (toast) => {
                        const icon = document.createElement('i');
                        icon.className = message.icon;
                        const title = toast.querySelector('.swal2-title');
                        if (title) {
                            title.insertBefore(icon, title.firstChild);
                            icon.style.marginLeft = '10px';
                        }
                        
                        toast.addEventListener('mouseenter', Swal.stopTimer);
                        toast.addEventListener('mouseleave', Swal.resumeTimer);
                    },
                    didClose: () => {
                        setTimeout(() => {
                            if (currentMessageIndex < branchMessages.length) {
                                showMessage();
                            }
                        }, 500);
                    }
                });
            };

            function getMessageIcon(type) {
                switch(type) {
                    case 'welcome':
                        return 'success';
                    case 'goal':
                        return 'info';
                    case 'announcement':
                        return 'info';
                    case 'holiday':
                        return 'warning';
                    default:
                        return 'info';
                }
            }

            function getMessageBackground(color) {
                switch(color) {
                    case 'danger':
                        return '#f8d7da';
                    case 'warning':
                        return '#fff3cd';
                    case 'success':
                        return '#d4edda';
                    case 'info':
                        return '#d1ecf1';
                    default:
                        return '#ffffff';
                }
            }

            // Start showing messages after a short delay to ensure page is fully loaded
            setTimeout(showMessage, 500);
        });
    </script>

    <style>
        .branch-message-container {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .branch-message-container i {
            margin-left: 10px;
            font-size: 1.2em;
            vertical-align: middle;
        }

        @media (max-width: 768px) {
            .branch-message-container {
                margin: 5px;
                padding: 10px;
            }
        }
    </style>
    {% endif %}

    <!-- Main Content -->
    <main class="container mt-4 flex-grow">
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block content %}{% endblock %}
    </main><!-- Footer المُحسَّن - تصميم أفقي -->
    <footer class="footer-area text-white mt-5 py-2" style="background-color: var(--accent);">
        <div class="container">
            <div class="d-flex align-items-center justify-content-between flex-wrap gap-3">
                <!-- معلومات الشركة والتواصل الاجتماعي -->
                <div class="d-flex align-items-center">
                    <h6 class="footer-title mb-0 me-3">
                        <i class="fas fa-building me-2"></i>
                        {{ company_info.name }}
                    </h6>
                    {% if company_info.facebook or company_info.twitter or company_info.instagram or company_info.linkedin %}
                    <div class="social-icons d-flex">
                        {% if company_info.facebook %}
                        <a href="{{ company_info.facebook }}" class="social-icon me-2" target="_blank" title="Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        {% endif %}
                        {% if company_info.twitter %}
                        <a href="{{ company_info.twitter }}" class="social-icon me-2" target="_blank" title="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        {% endif %}
                        {% if company_info.instagram %}
                        <a href="{{ company_info.instagram }}" class="social-icon me-2" target="_blank" title="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                        {% endif %}
                        {% if company_info.linkedin %}
                        <a href="{{ company_info.linkedin }}" class="social-icon me-2" target="_blank" title="LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>

                <!-- الروابط السريعة -->
                <div class="d-flex align-items-center">
                    <a href="{% url 'home' %}" class="footer-link mx-2">
                        <i class="fas fa-home"></i>
                    </a>
                    <a href="{% url 'about' %}" class="footer-link mx-2">
                        <i class="fas fa-info-circle"></i>
                    </a>
                    <a href="{% url 'contact' %}" class="footer-link mx-2">
                        <i class="fas fa-envelope"></i>
                    </a>
                </div>

                <!-- معلومات الاتصال -->
                <div class="d-flex align-items-center gap-3">
                    {% if company_info.phone %}
                    <a href="tel:{{ company_info.phone }}" class="footer-contact-item" title="رقم الهاتف">
                        <i class="fas fa-phone-alt"></i>
                        <span class="d-none d-md-inline small">{{ company_info.phone }}</span>
                    </a>
                    {% endif %}
                    {% if company_info.email %}
                    <a href="mailto:{{ company_info.email }}" class="footer-contact-item" title="البريد الإلكتروني">
                        <i class="fas fa-at"></i>
                        <span class="d-none d-md-inline small">{{ company_info.email }}</span>
                    </a>
                    {% endif %}
                    {% if company_info.address %}
                    <div class="footer-contact-item" title="العنوان">
                        <i class="fas fa-map-marker-alt"></i>
                        <span class="d-none d-md-inline small">{{ company_info.address }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- تفاصيل الشركة (وصف مختصر) -->
            {% if company_info.description %}
            <div class="text-center mt-2">
                <span class="small">{{ company_info.description }}</span>
            </div>
            {% endif %}

            <!-- حقوق النشر -->
            <div class="text-center mt-2">
                <p class="copyright-text mb-0 small">
                    <i class="far fa-copyright me-1"></i>
                    {{ current_year }}
                    {{ company_info.copyright_text|default:"جميع الحقوق محفوظة" }}
                </p>
            </div>
        </div>
    </footer>

    <style>
    /* تنسيق الجسم والمحتوى الرئيسي للصفحة */
    html, body {
        height: 100%;
        margin: 0;
    }

    body {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }

    main.container {
        flex: 1 0 auto;
    }

    /* أنماط التذييل المُحسَّن - تصميم أفقي */
    .footer-area {
        flex-shrink: 0;
        background: linear-gradient(to right, var(--accent), var(--dark));
        padding: 10px 0 5px 0 !important;
        width: 100%;
    }

    .footer-title {
        font-size: 0.9rem;
        font-weight: 600;
    }

    .social-icons {
        gap: 5px;
    }

    .social-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        color: white;
        background-color: rgba(255,255,255,0.1);
        transition: all 0.3s ease;
        text-decoration: none;
        font-size: 0.75rem;
    }

    .social-icon:hover {
        background-color: rgba(255,255,255,0.3);
        color: white;
        transform: translateY(-2px);
    }

    .footer-link {
        color: rgba(255,255,255,0.9);
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .footer-link:hover {
        color: white;
        transform: scale(1.1);
    }

    .footer-contact-item {
        color: rgba(255,255,255,0.9);
        text-decoration: none;
        transition: all 0.2s ease;
        font-size: 0.85rem;
    }

    .footer-contact-item:hover {
        color: white;
    }

    .copyright-text {
        font-size: 0.75rem;
        opacity: 0.9;
        border-top: 1px solid rgba(255,255,255,0.1);
        padding-top: 5px;
    }

    /* استجابة للشاشات الصغيرة */
    @media (max-width: 768px) {
        .footer-area {
            padding: 8px 0 4px 0 !important;
        }
        
        .footer-title {
            font-size: 0.8rem;
        }
        
        .social-icon {
            width: 22px;
            height: 22px;
            font-size: 0.7rem;
        }

        .copyright-text {
            font-size: 0.7rem;
        }
    }
    </style>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap Bundle JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/custom-theme-animations.js' %}"></script>

    <!-- Database Connection Test JS -->
    {% if 'database' in request.path %}
    <script src="{% static 'js/db_connection_test.js' %}"></script>
    {% endif %}

    <!-- Restore Success Alert -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // البحث عن رسائل النجاح المتعلقة بالاستعادة
            const messages = document.querySelectorAll('.alert-success');

            messages.forEach(function(messageElement) {
                const messageText = messageElement.textContent.trim();

                // التحقق من أن الرسالة تتعلق بالاستعادة
                if (messageText.includes('تم استعادة النسخة الاحتياطية بنجاح') ||
                    messageText.includes('تمت الاستعادة بنجاح') ||
                    messageText.includes('تم استعادة البيانات بنجاح') ||
                    messageText.includes('تمت استعادة البيانات بنجاح')) {

                    // إخفاء الرسالة العادية
                    messageElement.style.display = 'none';

                    // عرض SweetAlert مع التعليمات
                    Swal.fire({
                        title: 'تمت الاستعادة بنجاح! 🎉',
                        html: `
                            <div style="text-align: right; direction: rtl; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                                <p style="font-size: 16px; margin-bottom: 20px; color: #2c3e50;">
                                    <strong>لضمان ظهور جميع البيانات، يرجى اتباع إحدى الخطوات التالية:</strong>
                                </p>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                                    <p style="margin: 8px 0; color: #495057;">
                                        <i class="fas fa-sync-alt" style="color: #007bff; margin-left: 8px;"></i>
                                        <strong>1. تحديث الصفحة (F5)</strong>
                                    </p>
                                    <p style="margin: 8px 0; color: #495057;">
                                        <i class="fas fa-sign-in-alt" style="color: #28a745; margin-left: 8px;"></i>
                                        <strong>2. إعادة تسجيل الدخول</strong>
                                    </p>
                                    <p style="margin: 8px 0; color: #495057;">
                                        <i class="fas fa-clock" style="color: #ffc107; margin-left: 8px;"></i>
                                        <strong>3. انتظار دقيقة واحدة للتحديث التلقائي</strong>
                                    </p>
                                </div>
                                <p style="font-size: 14px; color: #6c757d; margin-top: 15px;">
                                    <i class="fas fa-info-circle" style="margin-left: 5px;"></i>
                                    هذا أمر طبيعي ويحدث بسبب التخزين المؤقت للبيانات
                                </p>
                            </div>
                        `,
                        icon: 'success',
                        showCancelButton: true,
                        confirmButtonText: '<i class="fas fa-sync-alt"></i> تحديث الصفحة الآن',
                        cancelButtonText: '<i class="fas fa-times"></i> إغلاق',
                        confirmButtonColor: '#007bff',
                        cancelButtonColor: '#6c757d',
                        width: '600px',
                        customClass: {
                            popup: 'rtl-popup',
                            title: 'rtl-title',
                            content: 'rtl-content'
                        },
                        showClass: {
                            popup: 'animate__animated animate__fadeInDown'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutUp'
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // تحديث الصفحة فوراً
                            location.reload();
                        } else {
                            // إضافة رسالة تذكير للمستخدم
                            const reminderToast = Swal.mixin({
                                toast: true,
                                position: 'top-end',
                                showConfirmButton: false,
                                timer: 5000,
                                timerProgressBar: true,
                                didOpen: (toast) => {
                                    toast.addEventListener('mouseenter', Swal.stopTimer)
                                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                                }
                            });

                            reminderToast.fire({
                                icon: 'info',
                                title: 'تذكير: قم بتحديث الصفحة لرؤية جميع البيانات'
                            });

                            // تحديث تلقائي بعد دقيقة واحدة
                            setTimeout(() => {
                                Swal.fire({
                                    title: 'تحديث تلقائي',
                                    text: 'سيتم تحديث الصفحة الآن لإظهار جميع البيانات',
                                    icon: 'info',
                                    timer: 3000,
                                    timerProgressBar: true,
                                    showConfirmButton: false
                                }).then(() => {
                                    location.reload();
                                });
                            }, 60000); // 60 ثانية
                        }
                    });
                }
            });
        });
    </script>

    <!-- CSS للـ RTL SweetAlert -->
    <style>
        .rtl-popup {
            direction: rtl !important;
            text-align: right !important;
        }

        .rtl-title {
            direction: rtl !important;
            text-align: center !important;
        }

        .rtl-content {
            direction: rtl !important;
            text-align: right !important;
        }

        .swal2-html-container {
            direction: rtl !important;
            text-align: right !important;
        }

        .swal2-actions {
            direction: ltr !important;
        }

        .swal2-confirm {
            margin-left: 10px !important;
        }
    </style>

    {% block extra_js %}{% endblock %}
</body>
</html>
