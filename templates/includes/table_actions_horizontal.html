{% comment %}
يتم استدعاء هذا القالب مع المتغيرات التالية:
view_perm: صلاحية العرض (مثال: 'customers.view_customer')
change_perm: صلاحية التعديل (مثال: 'customers.change_customer')
delete_perm: صلاحية الحذف (مثال: 'customers.delete_customer')
detail_url: رابط صفحة التفاصيل
update_url: رابط صفحة التعديل
delete_url: رابط صفحة الحذف
item_branch: فرع العنصر (customer.branch, order.branch, etc)
delete_message: رسالة تأكيد الحذف
{% endcomment %}

<div class="btn-group">
    {% if view_perm and perms|get_perm:view_perm %}
    <a href="{{ detail_url }}" class="btn btn-info btn-sm" title="عرض التفاصيل">
        <i class="fas fa-eye"></i>
    </a>
    {% endif %}

    {% if change_perm and perms|get_perm:change_perm %}
    {% if request.user.is_superuser or item_branch == request.user.branch %}
    <a href="{{ update_url }}" class="btn btn-primary btn-sm" title="تعديل">
        <i class="fas fa-edit"></i>
    </a>
    {% endif %}
    {% endif %}

    {% if delete_perm and perms|get_perm:delete_perm %}
    {% if request.user.is_superuser or item_branch == request.user.branch %}
    <a href="{{ delete_url }}" class="btn btn-danger btn-sm" title="حذف"
       onclick="return confirm('{{ delete_message|default:'هل أنت متأكد من الحذف؟' }}');">
        <i class="fas fa-trash"></i>
    </a>
    {% endif %}
    {% endif %}
</div>
